[server]
port = 8687
mode = "debug" # debug, release, test
max_export_limit = 1000
context = "/sase"
deploy_invite_url = "https://www-alpha.rongma.tech/%s"

[mongodb]
addr = 'mongodb.outside.rongma.tech:27017'
database = "alpha-rm_sase"
username = "alpha-rmclient"
password = "zo3RdOgGio*F"

[mongodb_sase_report]
addr = 'mongodb.outside.rongma.tech:27017'
database = 'alpha-sase_report'
username = 'alpha-rmclient'
password ='zo3RdOgGio*F'

[mongodb_client_instructions]
addr = 'mongodb.outside.rongma.tech:27017'
database = 'alpha-client_instructions'
username = 'alpha-rmclient'
password = 'zo3RdOgGio*F'

[mongodb_engine]
database = 'alpha-engines'
addr = 'mongodb.outside.rongma.tech:27017'
username = 'alpha-rmclient'
password = 'zo3RdOgGio*F'

[redis]
# 0-单机模式 1-集群模式
connection_mode = 0
addr = "192.168.111.185:36379"
password = ""
db = 0

[log]
level = "info"
path = "./logs"

[grpc_client_strategy]
is_cheack_cert = false
addr = '127.0.0.1:8080'

[grpc_group]
is_cheack_cert = false
addr = '192.168.111.185:25595'

[certs]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'
crt = "certs/client.crt"
key = "certs/client.key"
ca = "certs/ca.crt"
qax_grpc = 'certs/qianxin.crt'

[nginx_minio]
# 资源前缀，桶名前缀，比如 alpha-failed-upload，那么配 alpha-
resource_prefix = ''
addr = '**************:19000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'
# 下载地址，不要以 / 结尾
download_url = 'https://alpha-oss.rongma.tech'

[email]
port = 465
host = "smtpdm.aliyun.com"
user = "<EMAIL>"
password = "abcU7vOqKRq3"
alias_name = "戎码科技"