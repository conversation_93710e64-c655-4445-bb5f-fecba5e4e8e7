2025-07-03 10:42:23.362 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 10:42:24.368 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 10:42:24.371 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 10:42:24.375 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 10:42:24.375 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 10:42:24.375 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 10:42:24.375 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 10:42:24.375 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 10:42:24.375 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 10:42:24.376 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 10:42:24.376 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 10:42:24.376 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 10:42:24.376 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 10:42:24.377 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 10:42:24.379 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 10:42:24.379 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 10:42:24.379 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 10:58:25.408 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 10:58:25.412 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 10:58:38.222 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 10:58:39.202 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 10:58:39.204 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 10:58:39.206 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 10:58:39.206 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 10:58:39.206 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 10:58:39.206 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 10:58:39.206 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 10:58:39.206 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 10:58:39.206 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 10:58:39.206 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 10:58:39.206 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 10:58:39.206 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 10:58:39.206 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 10:58:39.206 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 10:58:39.207 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 10:58:39.207 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 10:58:39.207 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 10:58:39.208 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 10:58:39.209 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 10:58:39.209 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 10:58:39.209 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 11:03:21.016 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 11:03:21.018 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 11:03:34.378 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 11:03:35.366 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 11:03:35.368 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 11:03:35.370 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 11:03:35.371 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 11:03:35.371 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 11:03:35.371 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 11:03:35.371 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 11:03:35.371 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 11:03:35.373 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 11:03:35.374 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 11:03:35.374 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 11:03:35.374 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 11:08:18.148 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 11:08:18.150 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 11:08:30.785 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 11:08:31.765 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 11:08:31.768 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 11:08:31.770 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 11:08:31.770 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 11:08:31.770 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 11:08:31.770 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 11:08:31.770 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 11:08:31.770 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 11:08:31.772 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 11:08:31.773 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 11:08:31.773 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 11:08:31.773 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 11:11:33.272 ERROR [handler.go:505[BatchPauseMembers]] BatchPauseMembers Scan error: context canceled
2025-07-03 11:22:57.060 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 11:22:57.062 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 11:23:21.311 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 11:23:22.299 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 11:23:22.302 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 11:23:22.322 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 11:23:22.322 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 11:23:22.322 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 11:23:22.322 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 11:23:22.322 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 11:23:22.322 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 11:23:22.322 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 11:23:22.323 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 11:23:22.323 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 11:23:22.323 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 11:23:22.323 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 11:23:22.324 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 11:23:22.325 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 11:23:22.325 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 11:23:22.325 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 11:37:42.286 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 11:37:42.293 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 11:38:01.059 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 11:38:02.059 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 11:38:02.062 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 11:38:02.066 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 11:38:02.066 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 11:38:02.066 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 11:38:02.066 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 11:38:02.066 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 11:38:02.066 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 11:38:02.066 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 11:38:02.066 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 11:38:02.066 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 11:38:02.067 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 11:38:02.067 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 11:38:02.067 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 11:38:02.067 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 11:38:02.068 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 11:38:02.069 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 11:38:02.069 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 11:38:02.069 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 11:42:03.733 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 11:42:03.736 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 11:42:22.340 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 11:42:23.382 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 11:42:23.387 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 11:42:23.390 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 11:42:23.390 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 11:42:23.390 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 11:42:23.390 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 11:42:23.390 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 11:42:23.390 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 11:42:23.391 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 11:42:23.392 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 11:42:23.392 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 11:42:23.392 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 11:49:19.476 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 11:49:19.478 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 11:49:43.870 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 11:49:44.879 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 11:49:44.883 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 11:49:44.885 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 11:49:44.885 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 11:49:44.885 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 11:49:44.886 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 11:49:44.886 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 11:49:44.886 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 11:49:44.886 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 11:49:44.887 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 11:49:44.888 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 11:49:44.888 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 11:49:44.888 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 11:52:24.074 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 11:52:24.083 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 11:52:43.709 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 11:52:44.706 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 11:52:44.725 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 11:52:44.729 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 11:52:44.729 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 11:52:44.729 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 11:52:44.730 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 11:52:44.730 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 11:52:44.730 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 11:52:44.730 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 11:52:44.731 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 11:52:44.732 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 11:52:44.732 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 11:52:44.733 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 11:53:31.684 ERROR [handler.go:499[BatchPauseMembers]] BatchPauseMembers Scan error: context canceled
2025-07-03 11:54:42.576 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 11:54:42.578 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 11:55:00.556 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 11:55:01.543 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 11:55:01.546 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 11:55:01.548 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 11:55:01.548 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 11:55:01.548 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 11:55:01.548 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 11:55:01.548 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 11:55:01.548 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 11:55:01.549 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 11:55:01.550 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 11:55:01.551 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 11:55:01.551 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 11:55:01.551 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 11:55:55.915 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 11:55:55.935 ERROR [handler.go:498[BatchPauseMembers]] BatchPauseMembers Scan error: redis: client is closed
2025-07-03 11:55:55.951 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 11:56:13.671 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 11:56:14.642 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 11:56:14.645 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 11:56:14.646 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 11:56:14.646 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 11:56:14.646 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 11:56:14.646 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 11:56:14.646 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 11:56:14.646 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 11:56:14.647 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 11:56:14.647 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 11:56:14.647 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 11:56:14.647 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 11:56:14.648 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 11:56:14.649 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 11:56:14.649 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 11:56:14.649 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 13:54:18.667 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 13:54:18.670 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 13:54:35.584 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 13:54:36.584 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 13:54:36.587 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 13:54:36.589 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 13:54:36.589 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 13:54:36.589 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 13:54:36.589 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 13:54:36.589 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 13:54:36.589 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 13:54:36.590 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 13:54:36.590 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 13:54:36.590 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 13:54:36.590 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 13:54:36.592 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 13:54:36.593 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 13:54:36.593 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 13:54:36.593 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 13:55:40.555 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 13:55:40.557 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 13:55:54.268 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 13:55:55.273 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 13:55:55.276 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 13:55:55.278 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 13:55:55.278 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 13:55:55.278 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 13:55:55.279 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 13:55:55.279 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 13:55:55.279 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 13:55:55.279 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 13:55:55.281 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 13:55:55.282 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 13:55:55.282 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 13:55:55.282 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 14:01:01.548 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 14:01:01.551 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 14:01:16.481 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 14:01:17.472 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 14:01:17.475 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 14:01:17.477 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 14:01:17.477 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 14:01:17.477 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 14:01:17.477 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 14:01:17.477 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 14:01:17.477 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 14:01:17.478 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 14:01:17.478 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 14:01:17.479 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 14:01:17.480 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 14:01:17.480 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 14:01:17.480 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 14:05:03.634 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 14:05:03.636 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 14:05:19.179 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 14:05:20.173 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 14:05:20.181 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 14:05:20.183 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 14:05:20.183 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 14:05:20.183 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 14:05:20.183 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 14:05:20.183 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 14:05:20.184 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 14:05:20.184 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 14:05:20.184 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 14:05:20.184 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 14:05:20.184 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 14:05:20.184 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 14:05:20.184 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 14:05:20.185 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 14:05:20.186 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 14:05:20.186 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 14:05:20.186 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 14:07:13.780 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 14:07:13.786 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 14:07:33.289 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 14:07:34.294 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 14:07:34.296 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 14:07:34.298 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 14:07:34.298 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 14:07:34.298 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 14:07:34.298 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 14:07:34.299 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 14:07:34.299 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 14:07:34.299 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 14:07:34.300 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 14:07:34.302 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 14:07:34.302 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 14:07:34.302 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 14:10:01.596 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 14:10:01.599 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 14:10:15.549 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 14:10:16.541 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 14:10:16.545 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 14:10:16.547 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 14:10:16.547 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 14:10:16.547 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 14:10:16.547 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 14:10:16.547 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 14:10:16.547 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 14:10:16.547 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 14:10:16.548 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 14:10:16.548 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 14:10:16.548 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 14:10:16.548 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 14:10:16.549 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 14:10:16.549 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 14:10:16.549 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 14:10:16.549 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 14:14:09.073 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 14:14:09.076 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 14:14:27.693 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 14:14:28.741 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 14:14:28.769 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 14:14:28.771 INFO [common.go:118[Init]] 初始化模型实例...
2025-07-03 14:14:28.771 INFO [common.go:131[Init]] 办公设置仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:135[Init]] 应用分类仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:139[Init]] 应用标签仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:143[Init]] 门户应用分类仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:147[Init]] 门户应用分类关联仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:151[Init]] 应用基础仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:155[Init]] 应用标签关系仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:159[Init]] 应用管理员仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:172[Init]] 所有模型实例初始化完成
2025-07-03 14:14:28.771 INFO [common.go:176[Init]] 连接器集群仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:180[Init]] 连接器仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:184[Init]] 应用健康状态仓库已初始化
2025-07-03 14:14:28.771 INFO [common.go:187[Init]] 策略关联仓库已初始化
2025-07-03 14:14:28.772 INFO [common.go:190[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 14:14:28.772 INFO [common.go:194[Init]] 策略处置仓库已初始化
2025-07-03 14:14:28.772 INFO [common.go:197[Init]] 所有模型实例初始化完成
2025-07-03 14:14:28.772 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 14:14:28.772 INFO [common.go:204[Init]] ZTNA访问日志仓库已初始化
2025-07-03 14:14:28.772 INFO [common.go:207[Init]] 应用权限仓库已初始化
2025-07-03 14:14:28.772 INFO [common.go:211[Init]] 门户应用配置仓库已初始化
2025-07-03 14:14:28.772 INFO [common.go:214[Init]] 通知策略仓库已初始化
2025-07-03 14:14:28.772 INFO [common.go:217[Init]] 通知IM设置仓库已初始化
2025-07-03 14:14:28.772 INFO [common.go:220[Init]] 通知策略范围仓库已初始化
2025-07-03 14:14:28.772 INFO [common.go:223[Init]] 通知消息仓库已初始化
2025-07-03 14:14:28.773 INFO [routes.go:48[RegisterRoutes]] 开始注册API路由
2025-07-03 14:14:28.774 INFO [routes.go:505[RegisterRoutes]] API路由注册完成
2025-07-03 14:14:28.774 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 14:14:28.774 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 14:17:51.068 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 14:17:51.071 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 14:18:19.698 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 14:18:20.680 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 14:18:20.682 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 14:18:20.684 INFO [common.go:121[Init]] 初始化模型实例...
2025-07-03 14:18:20.684 INFO [common.go:134[Init]] 办公设置仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:138[Init]] 应用分类仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:142[Init]] 应用标签仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:146[Init]] 门户应用分类仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:150[Init]] 门户应用分类关联仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:154[Init]] 应用基础仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:158[Init]] 应用标签关系仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:162[Init]] 应用管理员仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:175[Init]] 所有模型实例初始化完成
2025-07-03 14:18:20.684 INFO [common.go:179[Init]] 连接器集群仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:183[Init]] 连接器仓库已初始化
2025-07-03 14:18:20.684 INFO [common.go:187[Init]] 应用健康状态仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:190[Init]] 策略关联仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:193[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:197[Init]] 策略处置仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 14:18:20.685 INFO [common.go:203[Init]] 所有模型实例初始化完成
2025-07-03 14:18:20.685 INFO [common.go:207[Init]] ZTNA访问日志仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:210[Init]] 应用权限仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:214[Init]] 门户应用配置仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:217[Init]] 通知策略仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:220[Init]] 通知IM设置仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:223[Init]] 通知策略范围仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:226[Init]] 通知消息仓库已初始化
2025-07-03 14:18:20.685 INFO [common.go:229[Init]] 下载渠道仓库已初始化
2025-07-03 14:18:20.686 INFO [routes.go:49[RegisterRoutes]] 开始注册API路由
2025-07-03 14:18:20.687 INFO [routes.go:514[RegisterRoutes]] API路由注册完成
2025-07-03 14:18:20.687 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 14:18:20.687 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 14:20:14.393 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 14:20:14.395 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 14:20:29.453 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 14:20:30.432 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 14:20:30.435 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 14:20:30.447 INFO [common.go:121[Init]] 初始化模型实例...
2025-07-03 14:20:30.447 INFO [common.go:134[Init]] 办公设置仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:138[Init]] 应用分类仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:142[Init]] 应用标签仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:146[Init]] 门户应用分类仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:150[Init]] 门户应用分类关联仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:154[Init]] 应用基础仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:158[Init]] 应用标签关系仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:162[Init]] 应用管理员仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:175[Init]] 所有模型实例初始化完成
2025-07-03 14:20:30.448 INFO [common.go:179[Init]] 连接器集群仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:183[Init]] 连接器仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:187[Init]] 应用健康状态仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:190[Init]] 策略关联仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:193[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:197[Init]] 策略处置仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 14:20:30.448 INFO [common.go:203[Init]] 所有模型实例初始化完成
2025-07-03 14:20:30.448 INFO [common.go:207[Init]] ZTNA访问日志仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:210[Init]] 应用权限仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:214[Init]] 门户应用配置仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:217[Init]] 通知策略仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:220[Init]] 通知IM设置仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:223[Init]] 通知策略范围仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:226[Init]] 通知消息仓库已初始化
2025-07-03 14:20:30.448 INFO [common.go:229[Init]] 下载渠道仓库已初始化
2025-07-03 14:20:30.449 INFO [routes.go:49[RegisterRoutes]] 开始注册API路由
2025-07-03 14:20:30.450 INFO [routes.go:514[RegisterRoutes]] API路由注册完成
2025-07-03 14:20:30.450 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 14:20:30.450 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 14:22:27.451 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 14:22:27.453 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 14:22:40.584 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 14:22:41.555 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 14:22:41.558 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 14:22:41.560 INFO [common.go:121[Init]] 初始化模型实例...
2025-07-03 14:22:41.560 INFO [common.go:134[Init]] 办公设置仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:138[Init]] 应用分类仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:142[Init]] 应用标签仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:146[Init]] 门户应用分类仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:150[Init]] 门户应用分类关联仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:154[Init]] 应用基础仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:158[Init]] 应用标签关系仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:162[Init]] 应用管理员仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:175[Init]] 所有模型实例初始化完成
2025-07-03 14:22:41.560 INFO [common.go:179[Init]] 连接器集群仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:183[Init]] 连接器仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:187[Init]] 应用健康状态仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:190[Init]] 策略关联仓库已初始化
2025-07-03 14:22:41.560 INFO [common.go:193[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 14:22:41.561 INFO [common.go:197[Init]] 策略处置仓库已初始化
2025-07-03 14:22:41.561 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 14:22:41.561 INFO [common.go:203[Init]] 所有模型实例初始化完成
2025-07-03 14:22:41.561 INFO [common.go:207[Init]] ZTNA访问日志仓库已初始化
2025-07-03 14:22:41.561 INFO [common.go:210[Init]] 应用权限仓库已初始化
2025-07-03 14:22:41.561 INFO [common.go:214[Init]] 门户应用配置仓库已初始化
2025-07-03 14:22:41.561 INFO [common.go:217[Init]] 通知策略仓库已初始化
2025-07-03 14:22:41.561 INFO [common.go:220[Init]] 通知IM设置仓库已初始化
2025-07-03 14:22:41.561 INFO [common.go:223[Init]] 通知策略范围仓库已初始化
2025-07-03 14:22:41.561 INFO [common.go:226[Init]] 通知消息仓库已初始化
2025-07-03 14:22:41.561 INFO [common.go:229[Init]] 下载渠道仓库已初始化
2025-07-03 14:22:41.563 INFO [routes.go:49[RegisterRoutes]] 开始注册API路由
2025-07-03 14:22:41.564 INFO [routes.go:514[RegisterRoutes]] API路由注册完成
2025-07-03 14:22:41.564 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 14:22:41.564 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 14:43:07.264 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 14:43:07.266 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 14:43:29.654 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 14:43:30.665 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 14:43:30.668 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 14:43:30.684 INFO [common.go:121[Init]] 初始化模型实例...
2025-07-03 14:43:30.684 INFO [common.go:134[Init]] 办公设置仓库已初始化
2025-07-03 14:43:30.684 INFO [common.go:138[Init]] 应用分类仓库已初始化
2025-07-03 14:43:30.684 INFO [common.go:142[Init]] 应用标签仓库已初始化
2025-07-03 14:43:30.684 INFO [common.go:146[Init]] 门户应用分类仓库已初始化
2025-07-03 14:43:30.684 INFO [common.go:150[Init]] 门户应用分类关联仓库已初始化
2025-07-03 14:43:30.684 INFO [common.go:154[Init]] 应用基础仓库已初始化
2025-07-03 14:43:30.684 INFO [common.go:158[Init]] 应用标签关系仓库已初始化
2025-07-03 14:43:30.684 INFO [common.go:162[Init]] 应用管理员仓库已初始化
2025-07-03 14:43:30.684 INFO [common.go:175[Init]] 所有模型实例初始化完成
2025-07-03 14:43:30.685 INFO [common.go:179[Init]] 连接器集群仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:183[Init]] 连接器仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:187[Init]] 应用健康状态仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:190[Init]] 策略关联仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:193[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:197[Init]] 策略处置仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:200[Init]] 所有模型实例初始化完成
2025-07-03 14:43:30.685 INFO [common.go:203[Init]] 所有模型实例初始化完成
2025-07-03 14:43:30.685 INFO [common.go:207[Init]] ZTNA访问日志仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:210[Init]] 应用权限仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:214[Init]] 门户应用配置仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:217[Init]] 通知策略仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:220[Init]] 通知IM设置仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:223[Init]] 通知策略范围仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:226[Init]] 通知消息仓库已初始化
2025-07-03 14:43:30.685 INFO [common.go:229[Init]] 下载渠道仓库已初始化
2025-07-03 14:43:30.686 INFO [routes.go:49[RegisterRoutes]] 开始注册API路由
2025-07-03 14:43:30.687 INFO [routes.go:514[RegisterRoutes]] API路由注册完成
2025-07-03 14:43:30.687 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 14:43:30.687 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 15:17:24.537 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 15:17:24.539 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 15:17:46.554 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 15:17:47.526 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 15:17:47.529 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 15:17:47.530 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 15:17:47.530 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 15:17:47.530 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 15:17:47.530 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 15:17:47.531 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 15:17:47.531 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 15:17:47.531 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 15:17:47.531 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 15:17:47.533 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 15:17:47.533 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 15:17:47.533 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 15:22:31.996 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 15:22:31.998 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 15:23:07.978 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 15:23:08.972 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 15:23:08.986 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 15:23:08.988 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 15:23:08.988 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 15:23:08.988 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 15:23:08.988 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 15:23:08.989 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 15:23:08.989 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 15:23:08.989 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 15:23:08.990 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 15:23:08.991 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 15:23:08.991 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 15:23:08.991 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 15:23:49.471 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 15:23:49.473 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 15:26:36.671 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 15:26:37.681 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 15:26:37.684 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 15:26:37.686 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 15:26:37.686 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 15:26:37.686 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 15:26:37.686 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 15:26:37.686 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 15:26:37.686 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 15:26:37.688 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 15:26:37.688 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 15:26:37.688 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 15:26:37.689 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 15:48:45.275 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 15:48:45.288 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 15:49:01.808 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 15:49:02.787 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 15:49:02.803 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 15:49:02.816 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 15:49:02.817 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 15:49:02.817 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 15:49:02.817 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 15:49:02.817 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 15:49:02.817 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 15:49:02.819 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 15:49:02.819 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 15:49:02.819 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 15:49:02.819 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 15:53:57.463 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 15:53:57.466 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 15:54:20.441 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 15:54:21.462 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 15:54:21.464 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 15:54:21.471 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 15:54:21.471 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 15:54:21.471 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 15:54:21.471 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 15:54:21.471 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 15:54:21.471 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 15:54:21.471 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 15:54:21.471 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 15:54:21.471 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 15:54:21.472 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 15:54:21.472 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 15:54:21.472 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 15:54:21.472 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 15:54:21.473 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 15:54:21.474 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 15:54:21.474 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 15:54:21.474 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 15:56:52.081 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 15:56:52.083 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 15:57:07.272 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 15:57:08.256 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 15:57:08.258 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 15:57:08.260 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 15:57:08.260 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 15:57:08.260 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 15:57:08.260 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 15:57:08.261 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 15:57:08.261 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 15:57:08.261 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 15:57:08.261 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 15:57:08.261 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 15:57:08.261 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 15:57:08.261 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 15:57:08.261 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 15:57:08.261 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 15:57:08.261 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 15:57:08.261 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 15:57:08.262 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 15:57:08.263 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 15:57:08.264 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 15:57:08.264 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:12:29.934 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:12:29.936 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:12:55.060 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:12:56.050 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:12:56.052 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:12:56.055 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:12:56.055 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:12:56.055 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:12:56.055 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:12:56.055 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:12:56.055 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:12:56.057 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:12:56.057 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:12:56.057 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:12:56.057 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:14:27.500 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:14:27.503 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:15:52.281 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:15:53.256 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:15:53.262 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:15:53.264 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:15:53.264 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:15:53.264 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:15:53.264 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:15:53.264 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:15:53.264 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:15:53.266 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:15:53.267 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:15:53.267 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:15:53.268 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:19:16.103 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:19:16.105 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:19:33.909 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:19:35.102 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:19:35.135 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:19:35.142 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:19:35.142 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:19:35.142 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:19:35.142 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:19:35.142 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:19:35.142 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:19:35.142 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:19:35.142 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:19:35.142 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:19:35.142 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:19:35.143 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:19:35.143 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:19:35.143 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:19:35.143 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:19:35.143 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:19:35.143 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:19:35.143 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:19:35.143 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:19:35.143 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:19:35.143 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:19:35.143 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:19:35.143 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:19:35.144 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:19:35.144 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:19:35.144 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:19:35.144 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:19:35.144 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:19:35.150 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:19:35.152 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:19:35.152 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:19:35.152 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:19:59.779 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:19:59.782 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:20:15.438 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:20:16.424 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:20:16.427 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:20:16.429 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:20:16.429 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:20:16.429 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:20:16.429 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:20:16.429 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:20:16.429 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:20:16.431 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:20:16.432 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:20:16.432 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:20:16.432 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:21:15.516 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:21:15.524 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:21:30.151 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:21:31.139 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:21:31.141 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:21:31.172 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:21:31.172 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:21:31.172 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:21:31.172 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:21:31.173 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:21:31.173 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:21:31.173 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:21:31.174 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:21:31.174 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:21:31.174 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:21:31.174 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:24:29.884 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:24:29.886 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:24:51.001 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:24:52.041 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:24:52.047 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:24:52.049 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:24:52.049 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:24:52.049 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:24:52.049 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:24:52.049 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:24:52.049 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:24:52.049 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:24:52.049 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:24:52.049 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:24:52.049 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:24:52.050 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:24:52.050 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:24:52.050 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:24:52.050 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:24:52.051 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:24:52.051 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:24:52.051 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:24:52.051 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:26:11.362 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:26:11.370 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:26:26.753 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:26:27.740 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:26:27.743 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:26:27.752 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:26:27.752 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:26:27.752 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:26:27.752 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:26:27.752 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:26:27.752 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:26:27.752 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:26:27.753 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:26:27.753 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:26:27.753 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:26:27.753 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:26:27.754 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:26:27.755 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:26:27.755 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:26:27.755 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:27:22.629 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:27:22.635 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:27:39.333 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:27:40.325 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:27:40.328 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:27:40.330 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:27:40.331 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:27:40.331 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:27:40.331 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:27:40.331 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:27:40.331 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:27:40.332 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:27:40.333 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:27:40.333 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:27:40.333 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:28:13.574 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:28:13.577 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:28:27.476 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:28:28.477 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:28:28.479 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:28:28.481 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:28:28.481 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:28:28.481 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:28:28.481 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:28:28.481 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:28:28.481 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:28:28.482 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:28:28.483 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:28:28.483 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:28:28.483 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:28:57.645 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:28:57.646 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:29:12.105 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:29:13.081 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:29:13.084 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:29:13.085 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:29:13.085 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:29:13.085 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:29:13.085 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:29:13.085 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:29:13.085 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:29:13.085 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:29:13.086 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:29:13.086 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:29:13.086 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:29:13.086 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:29:13.088 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:29:13.101 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:29:13.101 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:29:13.101 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:29:55.021 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:29:55.024 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:30:09.494 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:30:10.464 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:30:10.466 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:30:10.468 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:30:10.468 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:30:10.468 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:30:10.468 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:30:10.469 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:30:10.469 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:30:10.469 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:30:10.470 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:30:10.471 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:30:10.471 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:30:10.471 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:30:52.881 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:30:52.883 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:31:07.276 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:31:08.268 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:31:08.270 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:31:08.272 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:31:08.272 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:31:08.272 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:31:08.272 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:31:08.273 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:31:08.273 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:31:08.273 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:31:08.274 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:31:08.275 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:31:08.275 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:31:08.275 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:31:26.827 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:31:26.830 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:31:41.423 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:31:42.402 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:31:42.405 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:31:42.407 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:31:42.407 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:31:42.407 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:31:42.407 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:31:42.407 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:31:42.407 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:31:42.407 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:31:42.407 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:31:42.407 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:31:42.407 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:31:42.408 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:31:42.408 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:31:42.408 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:31:42.408 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:31:42.409 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:31:42.410 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:31:42.410 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:31:42.410 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:32:59.863 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:32:59.866 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:33:19.425 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:33:20.439 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:33:20.443 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:33:20.447 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:33:20.447 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:33:20.447 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:33:20.447 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:33:20.447 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:33:20.447 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:33:20.455 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:33:20.457 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:33:20.457 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:33:20.457 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:34:53.492 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:34:53.494 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:35:14.815 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:35:15.806 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:35:15.809 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:35:15.811 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:35:15.811 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:35:15.811 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:35:15.811 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:35:15.811 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:35:15.811 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:35:15.811 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:35:15.811 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:35:15.811 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:35:15.811 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:35:15.812 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:35:15.812 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:35:15.812 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:35:15.812 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:35:15.814 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:35:15.815 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:35:15.815 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:35:15.815 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:36:07.810 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:36:07.812 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:36:22.217 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:36:23.197 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:36:23.200 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:36:23.207 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:36:23.207 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:36:23.207 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:36:23.207 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:36:23.207 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:36:23.207 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:36:23.207 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:36:23.207 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:36:23.208 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:36:23.208 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:36:23.208 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:36:23.208 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:36:23.209 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:36:23.210 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:36:23.210 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:36:23.210 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:38:14.362 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:38:14.369 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:38:32.475 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:38:33.455 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:38:33.457 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:38:33.459 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:38:33.459 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:38:33.459 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:38:33.459 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:38:33.460 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:38:33.460 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:38:33.460 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:38:33.461 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:38:33.462 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:38:33.462 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:38:33.462 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:40:22.943 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:40:22.945 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:40:43.671 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:40:44.658 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:40:44.660 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:40:44.666 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:40:44.666 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:40:44.666 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:40:44.666 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:40:44.666 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:40:44.666 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:40:44.667 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:40:44.668 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:40:44.668 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:40:44.668 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:42:19.032 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:42:19.034 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:42:28.171 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:42:29.141 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:42:29.144 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:42:29.146 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:42:29.146 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:42:29.146 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:42:29.146 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:42:29.146 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:42:29.146 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:42:29.147 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:42:29.147 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:42:29.147 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:42:29.147 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:42:29.147 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:42:29.148 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:42:29.148 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:42:29.148 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:42:29.148 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:44:23.943 WARN [handler.go:527[func1]] CustomImportDepartments error: row 3 data incomplete
2025-07-03 16:44:23.955 INFO [handler.go:742[batchDeleteDepartmentUserRelation]] batch delete 1 relations successfully
2025-07-03 16:44:23.959 INFO [handler.go:752[batchUpdateUser]] batch update 1 users successfully
2025-07-03 16:44:23.961 INFO [handler.go:773[batchCreateDepartmentUserRelation]] batch create 1 relations successfully
2025-07-03 16:48:28.283 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 16:48:28.284 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 16:49:06.787 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 16:49:07.774 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 16:49:07.777 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 16:49:07.779 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 16:49:07.779 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 16:49:07.779 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 16:49:07.779 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 16:49:07.779 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 16:49:07.779 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 16:49:07.781 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 16:49:07.782 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 16:49:07.782 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 16:49:07.782 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 16:49:15.808 WARN [handler.go:527[func1]] CustomImportDepartments error: row 3 data incomplete
2025-07-03 16:49:15.820 INFO [handler.go:742[batchDeleteDepartmentUserRelation]] batch delete 1 relations successfully
2025-07-03 16:49:15.835 INFO [handler.go:752[batchUpdateUser]] batch update 1 users successfully
2025-07-03 16:49:15.840 INFO [handler.go:773[batchCreateDepartmentUserRelation]] batch create 1 relations successfully
2025-07-03 17:02:09.527 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:02:09.529 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:02:53.271 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:02:54.253 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:02:54.281 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:02:54.284 INFO [common.go:125[Init]] 初始化模型实例...
2025-07-03 17:02:54.284 INFO [common.go:138[Init]] 办公设置仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:142[Init]] 应用分类仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:146[Init]] 应用标签仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:150[Init]] 门户应用分类仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:154[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:158[Init]] 应用基础仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:162[Init]] 应用标签关系仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:166[Init]] 应用管理员仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:179[Init]] 所有模型实例初始化完成
2025-07-03 17:02:54.284 INFO [common.go:183[Init]] 连接器集群仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:187[Init]] 连接器仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:191[Init]] 应用健康状态仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:194[Init]] 策略关联仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:197[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:201[Init]] 策略处置仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:204[Init]] 所有模型实例初始化完成
2025-07-03 17:02:54.284 INFO [common.go:207[Init]] 所有模型实例初始化完成
2025-07-03 17:02:54.284 INFO [common.go:211[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:214[Init]] 应用权限仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:218[Init]] 门户应用配置仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:221[Init]] 通知策略仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:224[Init]] 通知IM设置仓库已初始化
2025-07-03 17:02:54.284 INFO [common.go:227[Init]] 通知策略范围仓库已初始化
2025-07-03 17:02:54.285 INFO [common.go:230[Init]] 通知消息仓库已初始化
2025-07-03 17:02:54.285 INFO [common.go:233[Init]] 下载渠道仓库已初始化
2025-07-03 17:02:54.285 INFO [common.go:237[Init]] 企业设置仓库已初始化
2025-07-03 17:02:54.296 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:02:54.297 INFO [routes.go:527[RegisterRoutes]] API路由注册完成
2025-07-03 17:02:54.297 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:02:54.297 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:17:40.318 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:17:40.320 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:18:01.788 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:18:02.780 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:18:02.783 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:18:02.785 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:18:02.785 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:18:02.785 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:18:02.785 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:18:02.785 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:18:02.785 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:18:02.786 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:18:02.786 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:18:02.786 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:18:02.786 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:18:02.786 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:18:02.786 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:18:02.786 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:18:02.786 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:18:02.786 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:18:02.786 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:18:02.788 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:18:02.788 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:18:02.788 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:18:48.557 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:18:48.563 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:19:02.196 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:19:03.178 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:19:03.181 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:19:03.183 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:19:03.183 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:19:03.183 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:19:03.183 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:19:03.183 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:19:03.183 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:19:03.185 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:19:03.186 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:19:03.186 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:19:03.186 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:19:44.733 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:19:44.735 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:20:00.637 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:20:01.642 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:20:01.644 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:20:01.646 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:20:01.646 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:20:01.646 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:20:01.646 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:20:01.647 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:20:01.647 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:20:01.647 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:20:01.647 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:20:01.647 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:20:01.647 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:20:01.647 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:20:01.647 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:20:01.647 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:20:01.647 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:20:01.647 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:20:01.648 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:20:01.648 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:20:01.648 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:20:01.648 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:23:14.914 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:23:14.916 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:23:28.558 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:23:29.560 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:23:29.563 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:23:29.565 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:23:29.565 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:23:29.565 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:23:29.565 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:23:29.565 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:23:29.565 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:23:29.566 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:23:29.566 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:23:29.566 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:23:29.566 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:23:29.570 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:23:29.571 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:23:29.571 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:23:29.571 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:25:16.468 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:25:16.472 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:25:29.456 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:25:30.440 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:25:30.442 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:25:30.444 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:25:30.444 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:25:30.444 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:25:30.444 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:25:30.444 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:25:30.444 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:25:30.444 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:25:30.444 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:25:30.444 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:25:30.444 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:25:30.445 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:25:30.445 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:25:30.445 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:25:30.445 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:25:30.456 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:25:30.457 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:25:30.457 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:25:30.457 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:27:33.722 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:27:33.725 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:27:48.116 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:27:49.106 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:27:49.108 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:27:49.111 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:27:49.111 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:27:49.111 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:27:49.111 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:27:49.111 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:27:49.111 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:27:49.111 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:27:49.111 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:27:49.112 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:27:49.112 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:27:49.112 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:27:49.112 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:27:49.113 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:27:49.114 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:27:49.114 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:27:49.114 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:28:44.247 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:28:44.249 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:28:57.597 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:28:58.591 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:28:58.594 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:28:58.596 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:28:58.596 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:28:58.596 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:28:58.596 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:28:58.597 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:28:58.597 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:28:58.597 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:28:58.601 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:28:58.602 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:28:58.602 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:28:58.602 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:29:37.352 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:29:37.372 ERROR [handler.go:540[BatchPauseMembers]] BatchPauseMembers Scan error: redis: client is closed
2025-07-03 17:29:37.388 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:29:49.649 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:29:50.618 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:29:50.625 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:29:50.627 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:29:50.627 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:29:50.627 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:29:50.627 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:29:50.628 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:29:50.628 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:29:50.628 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:29:50.628 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:29:50.630 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:29:50.630 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:29:50.630 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:30:25.616 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:30:25.636 ERROR [handler.go:539[BatchPauseMembers]] BatchPauseMembers Scan error: redis: client is closed
2025-07-03 17:30:25.638 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:31:18.353 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:31:19.340 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:31:19.343 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:31:19.345 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:31:19.345 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:31:19.345 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:31:19.345 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:31:19.345 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:31:19.345 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:31:19.347 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:31:19.347 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:31:19.347 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:31:19.347 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:31:36.216 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:31:36.237 ERROR [handler.go:541[BatchPauseMembers]] BatchPauseMembers Scan error: redis: client is closed
2025-07-03 17:31:36.253 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:32:58.852 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:32:59.836 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:32:59.839 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:32:59.857 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:32:59.857 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:32:59.857 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:32:59.857 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:32:59.857 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:32:59.857 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:32:59.859 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:32:59.860 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:32:59.860 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:32:59.860 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:33:45.098 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:33:45.100 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:33:58.201 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:33:59.176 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:33:59.179 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:33:59.180 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:33:59.180 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:33:59.180 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:33:59.180 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:33:59.180 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:33:59.180 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:33:59.180 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:33:59.180 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:33:59.181 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:33:59.181 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:33:59.181 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:33:59.181 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:33:59.182 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:33:59.184 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:33:59.184 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:33:59.184 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:34:13.826 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:34:13.847 ERROR [handler.go:541[BatchPauseMembers]] BatchPauseMembers Scan error: redis: client is closed
2025-07-03 17:34:13.863 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:35:57.229 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:35:58.208 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:35:58.210 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:35:58.213 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:35:58.213 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:35:58.213 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:35:58.213 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:35:58.213 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:35:58.213 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:35:58.214 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:35:58.215 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:35:58.215 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:35:58.215 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:37:14.555 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:37:14.558 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:37:27.790 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:37:28.766 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:37:28.770 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:37:28.772 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:37:28.772 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:37:28.772 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:37:28.772 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:37:28.773 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:37:28.773 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:37:28.773 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:37:28.773 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:37:28.775 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:37:28.775 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:37:28.776 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:37:28.776 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:37:39.091 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:37:39.092 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:37:51.621 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:37:52.621 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:37:52.624 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:37:52.630 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:37:52.630 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:37:52.630 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:37:52.630 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:37:52.630 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:37:52.630 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:37:52.631 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:37:52.631 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:37:52.631 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:37:52.631 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:37:52.638 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:37:52.639 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:37:52.640 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:37:52.640 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:39:49.831 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:39:49.833 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:40:03.593 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:40:04.577 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:40:04.582 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:40:04.584 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:40:04.584 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:40:04.584 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:40:04.584 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:40:04.584 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:40:04.585 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:40:04.585 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:40:04.585 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:40:04.585 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:40:04.586 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:40:04.588 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:40:04.588 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:40:04.588 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:40:30.513 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:40:30.533 ERROR [handler.go:542[BatchPauseMembers]] BatchPauseMembers Scan redis error: redis: client is closed
2025-07-03 17:40:30.549 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:40:45.857 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:40:46.838 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:40:46.841 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:40:46.846 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:40:46.846 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:40:46.846 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:40:46.846 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:40:46.846 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:40:46.846 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:40:46.846 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:40:46.846 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:40:46.846 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:40:46.846 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:40:46.847 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:40:46.847 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:40:46.847 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:40:46.847 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:40:46.852 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:40:46.853 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:40:46.854 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:40:46.854 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:42:37.533 ERROR [handler.go:543[BatchPauseMembers]] BatchPauseMembers Scan redis error: context canceled
2025-07-03 17:42:44.547 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:42:44.556 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:49:29.932 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:49:30.949 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:49:30.955 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:49:30.957 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:49:30.957 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:49:30.957 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:49:30.957 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:49:30.958 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:49:30.958 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:49:30.958 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:49:30.964 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:49:30.966 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:49:30.966 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:49:30.966 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:50:41.112 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:50:41.112 ERROR [handler.go:539[BatchPauseMembers]] BatchPauseMembers redis scan error: redis: client is closed
2025-07-03 17:50:41.132 ERROR [handler.go:539[BatchPauseMembers]] BatchPauseMembers redis scan error: redis: client is closed
2025-07-03 17:50:41.138 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:51:10.051 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:51:11.054 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:51:11.056 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:51:11.060 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:51:11.060 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:51:11.060 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:51:11.060 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:51:11.060 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:51:11.060 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:51:11.062 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:51:11.531 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:51:11.531 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:51:11.531 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:58:55.124 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:58:55.126 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 17:59:16.174 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 17:59:17.166 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 17:59:17.169 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 17:59:17.173 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 17:59:17.174 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 17:59:17.174 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 17:59:17.174 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 17:59:17.174 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 17:59:17.174 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 17:59:17.176 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 17:59:17.177 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 17:59:17.177 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 17:59:17.177 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 17:59:47.657 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 17:59:47.677 ERROR [handler.go:540[BatchPauseMembers]] BatchPauseMembers redis scan error: redis: client is closed
2025-07-03 17:59:47.693 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:00:25.355 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:00:26.344 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:00:26.347 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:00:26.349 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:00:26.349 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:00:26.349 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:00:26.349 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:00:26.350 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:00:26.350 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:00:26.350 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:00:26.351 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:00:26.351 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:00:26.351 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:00:26.352 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:00:36.856 ERROR [handler.go:540[BatchPauseMembers]] BatchPauseMembers redis scan error: context canceled
2025-07-03 18:01:22.925 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:01:22.933 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:01:38.652 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:01:39.660 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:01:39.662 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:01:39.664 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:01:39.664 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:01:39.664 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:01:39.664 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:01:39.664 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:01:39.664 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:01:39.664 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:01:39.664 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:01:39.664 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:01:39.665 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:01:39.665 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:01:39.665 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:01:39.665 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:01:39.666 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:01:39.666 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:01:39.667 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:01:39.668 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:01:39.668 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:01:39.668 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:01:49.267 ERROR [handler.go:541[func1]] BatchPauseMembers redis scan error: context canceled
2025-07-03 18:02:15.904 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:02:15.907 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:02:28.635 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:02:29.653 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:02:29.655 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:02:29.677 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:02:29.677 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:02:29.677 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:02:29.677 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:02:29.677 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:02:29.677 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:02:29.679 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:02:29.680 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:02:29.680 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:02:29.680 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:20:48.775 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:20:48.782 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:21:04.264 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:21:05.307 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:21:05.310 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:21:05.320 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:21:05.320 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:21:05.321 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:21:05.321 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:21:05.321 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:21:05.321 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:21:05.323 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:21:05.324 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:21:05.324 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:21:05.324 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:21:46.599 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:21:46.601 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:22:00.279 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:22:01.255 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:22:01.259 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:22:01.263 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:22:01.264 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:22:01.264 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:22:01.264 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:22:01.264 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:22:01.264 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:22:01.266 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:22:01.267 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:22:01.267 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:22:01.267 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:23:21.195 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:23:21.216 ERROR [handler.go:560[BatchPauseMembers]] BatchPauseMembers redis scan error: redis: client is closed
2025-07-03 18:23:21.223 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:23:34.659 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:23:35.629 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:23:35.631 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:23:35.633 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:23:35.633 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:23:35.633 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:23:35.633 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:23:35.633 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:23:35.633 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:23:35.634 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:23:35.634 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:23:35.635 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:23:35.635 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:23:35.635 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:23:35.635 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:25:56.208 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:25:56.208 ERROR [handler.go:542[BatchPauseMembers]] BatchPauseMembers redis scan error: redis: client is closed
2025-07-03 18:25:56.210 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:26:11.620 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:26:12.601 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:26:12.619 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:26:12.649 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:26:12.650 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:26:12.650 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:26:12.650 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:26:12.650 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:26:12.650 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:26:12.652 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:26:12.653 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:26:12.653 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:26:12.653 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:27:14.959 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:27:14.961 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:27:28.038 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:27:29.044 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:27:29.048 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:27:29.051 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:27:29.051 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:27:29.051 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:27:29.051 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:27:29.051 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:27:29.051 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:27:29.053 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:27:29.057 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:27:29.057 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:27:29.057 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:29:47.969 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:29:47.971 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:30:02.708 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:30:03.684 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:30:03.695 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:30:03.697 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:30:03.697 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:30:03.697 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:30:03.697 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:30:03.697 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:30:03.697 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:30:03.699 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:30:03.700 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:30:03.700 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:30:03.700 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:31:06.488 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:31:06.490 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:31:19.543 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:31:20.555 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:31:20.557 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:31:20.559 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:31:20.560 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:31:20.560 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:31:20.560 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:31:20.560 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:31:20.560 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:31:20.561 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:31:20.562 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:31:20.563 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:31:20.563 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:31:20.563 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:32:19.532 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:32:19.534 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:32:31.636 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:32:32.642 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:32:32.654 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:32:32.671 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:32:32.671 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:32:32.671 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:32:32.671 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:32:32.671 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:32:32.671 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:32:32.672 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:32:32.672 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:32:32.672 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:32:32.672 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:32:32.674 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:32:32.676 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:32:32.676 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:32:32.676 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:34:00.022 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:34:00.034 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:34:14.083 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:34:15.055 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:34:15.057 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:34:15.059 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:34:15.059 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:34:15.059 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:34:15.059 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:34:15.059 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:34:15.059 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:34:15.059 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:34:15.059 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:34:15.059 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:34:15.059 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:34:15.059 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:34:15.060 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:34:15.060 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:34:15.060 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:34:15.061 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:34:15.062 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:34:15.062 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:34:15.062 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:35:02.661 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:35:02.664 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:35:16.053 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:35:17.055 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:35:17.057 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:35:17.086 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:35:17.086 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:35:17.086 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:35:17.086 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:35:17.086 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:35:17.086 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:35:17.086 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:35:17.086 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:35:17.087 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:35:17.087 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:35:17.087 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:35:17.087 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:35:17.105 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:35:17.105 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:35:17.105 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:35:17.106 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:37:40.801 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:37:40.804 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:37:54.714 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:37:55.698 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:37:55.700 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:37:55.702 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:37:55.702 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:37:55.702 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:37:55.702 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:37:55.702 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:37:55.702 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:37:55.704 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:37:55.704 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:37:55.705 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:37:55.705 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:38:32.612 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:38:32.621 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:38:46.095 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:38:47.085 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:38:47.087 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:38:47.090 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:38:47.090 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:38:47.090 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:38:47.090 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:38:47.090 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:38:47.090 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:38:47.092 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:38:47.092 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:38:47.093 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:38:47.093 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:39:10.421 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:39:10.424 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:39:40.220 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:39:41.195 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:39:41.198 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:39:41.203 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:39:41.204 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:39:41.204 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:39:41.204 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:39:41.204 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:39:41.204 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:39:41.216 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:39:41.218 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:39:41.218 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:39:41.218 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:43:31.228 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:43:31.231 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:43:43.484 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:43:44.456 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:43:44.458 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:43:44.460 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:43:44.460 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:43:44.460 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:43:44.460 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:43:44.460 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:43:44.460 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:43:44.460 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:43:44.460 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:43:44.460 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:43:44.460 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:43:44.460 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:43:44.460 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:43:44.461 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:43:44.461 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:43:44.461 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:43:44.462 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:43:44.463 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:43:44.463 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:43:44.463 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:45:32.740 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:45:32.743 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:45:46.490 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:45:47.544 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:45:47.547 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:45:47.551 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:45:47.551 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:45:47.551 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:45:47.551 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:45:47.551 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:45:47.552 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:45:47.552 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:45:47.552 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:45:47.552 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:45:47.552 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:45:47.552 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:45:47.552 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:45:47.552 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:45:47.552 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:45:47.552 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:45:47.553 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:45:47.555 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:45:47.555 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:45:47.555 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:48:04.052 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:48:04.072 ERROR [handler.go:536[BatchPauseMembers]] BatchPauseMembers Scan error: redis: client is closed
2025-07-03 18:48:04.088 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:48:18.103 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:48:19.083 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:48:19.085 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:48:19.091 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:48:19.091 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:48:19.091 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:48:19.091 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:48:19.091 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:48:19.091 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:48:19.093 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:48:19.093 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:48:19.093 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:48:19.093 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:49:41.868 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:49:41.889 ERROR [handler.go:542[BatchPauseMembers]] BatchPauseMembers Scan error: redis: client is closed
2025-07-03 18:49:41.892 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:49:56.109 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:49:57.079 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:49:57.081 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:49:57.083 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:49:57.083 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:49:57.083 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:49:57.083 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:49:57.084 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:49:57.084 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:49:57.084 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:49:57.085 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:49:57.085 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:49:57.085 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:49:57.086 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:52:05.502 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:52:05.523 ERROR [handler.go:542[BatchPauseMembers]] BatchPauseMembers Scan error: redis: client is closed
2025-07-03 18:52:05.540 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 18:52:19.948 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 18:52:20.935 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 18:52:20.937 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 18:52:20.939 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 18:52:20.939 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 18:52:20.939 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 18:52:20.939 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 18:52:20.940 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 18:52:20.940 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 18:52:20.940 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 18:52:20.941 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 18:52:20.942 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 18:52:20.942 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 18:52:20.942 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 18:52:36.629 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 18:52:36.650 ERROR [handler.go:542[BatchPauseMembers]] BatchPauseMembers Scan error: redis: client is closed
2025-07-03 18:52:36.666 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 19:07:34.921 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 19:07:35.921 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 19:07:35.923 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 19:07:35.925 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 19:07:35.925 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 19:07:35.925 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 19:07:35.925 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 19:07:35.925 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 19:07:35.925 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 19:07:35.925 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 19:07:35.926 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 19:07:35.926 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 19:07:35.926 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 19:07:35.926 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 19:07:35.928 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 19:07:35.929 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 19:07:35.929 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 19:07:35.929 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 19:10:17.636 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 19:10:17.638 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 19:10:31.216 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 19:10:32.197 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 19:10:32.199 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 19:10:32.202 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 19:10:32.202 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 19:10:32.202 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 19:10:32.202 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 19:10:32.202 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 19:10:32.202 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 19:10:32.204 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 19:10:32.205 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 19:10:32.205 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 19:10:32.205 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 19:58:33.680 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 19:58:33.682 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 19:59:00.044 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 19:59:01.011 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 19:59:01.014 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 19:59:01.016 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 19:59:01.016 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 19:59:01.016 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 19:59:01.016 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 19:59:01.016 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 19:59:01.016 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 19:59:01.017 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 19:59:17.863 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 19:59:18.834 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 19:59:18.848 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 19:59:18.858 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 19:59:18.858 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 19:59:18.858 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 19:59:18.858 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 19:59:18.858 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 19:59:18.858 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 19:59:18.858 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 19:59:18.858 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 19:59:18.858 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 19:59:18.858 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 19:59:18.859 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 19:59:18.859 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 19:59:18.859 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 19:59:18.859 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 19:59:18.860 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 19:59:18.861 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 19:59:18.861 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 19:59:18.861 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 19:59:44.245 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 19:59:44.280 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:00:05.310 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:00:06.301 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:00:06.313 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:00:06.315 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:00:06.315 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:00:06.315 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:00:06.315 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:00:06.315 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:00:06.315 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:00:06.320 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:00:06.321 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 20:00:06.321 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:00:06.321 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:00:47.150 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:00:47.157 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:01:00.593 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:01:01.572 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:01:01.574 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:01:01.576 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:01:01.576 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:01:01.576 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:01:01.576 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:01:01.576 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:01:01.576 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:01:01.578 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:01:01.859 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 20:01:01.859 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:01:01.859 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:01:52.869 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:01:52.876 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:02:03.853 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:02:04.824 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:02:04.826 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:02:04.828 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:02:04.828 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:02:04.828 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:02:04.828 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:02:04.828 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:02:04.828 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:02:04.829 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:02:04.829 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:02:04.829 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:02:04.829 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:02:04.829 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:02:04.829 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:02:04.829 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:02:04.830 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:02:04.830 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 20:02:04.830 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:02:04.831 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:02:44.050 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:02:44.058 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:02:57.999 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:02:58.981 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:02:58.983 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:02:58.985 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:02:58.985 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:02:58.985 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:02:58.985 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:02:58.986 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:02:58.986 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:02:58.986 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:02:58.986 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:02:58.986 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:02:58.986 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:02:58.986 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:02:58.986 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:02:58.986 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:02:58.986 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:02:58.986 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:02:58.987 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:02:58.988 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 20:02:58.988 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:02:58.988 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:03:28.924 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:03:28.927 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:03:42.074 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:03:43.054 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:03:43.057 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:03:43.059 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:03:43.059 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:03:43.059 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:03:43.059 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:03:43.060 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:03:43.060 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:03:43.060 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:03:43.060 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:03:43.060 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:03:43.060 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:03:43.060 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:03:43.060 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:03:43.060 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:03:43.060 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:03:43.060 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:03:43.061 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:03:43.062 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 20:03:43.062 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:03:43.062 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:04:46.043 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:04:46.046 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:04:59.274 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:05:00.255 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:05:00.271 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:05:00.273 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:05:00.273 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:05:00.273 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:05:00.273 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:05:00.273 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:05:00.273 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:05:00.273 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:05:00.273 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:05:00.273 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:05:00.273 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:05:00.273 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:05:00.273 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:05:00.274 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:05:00.274 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:05:00.274 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:05:00.275 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:05:00.277 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 20:05:00.277 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:05:00.277 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:06:21.837 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:06:21.839 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:06:36.899 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:06:37.884 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:06:37.895 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:06:37.898 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:06:37.898 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:06:37.898 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:06:37.898 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:06:37.898 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:06:37.898 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:06:37.900 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:06:37.901 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 20:06:37.901 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:06:37.901 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:07:19.456 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:07:19.458 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:07:34.279 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:07:35.265 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:07:35.267 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:07:35.269 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:07:35.269 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:07:35.269 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:07:35.269 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:07:35.270 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:07:35.270 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:07:35.270 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:07:35.271 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:07:35.272 INFO [routes.go:528[RegisterRoutes]] API路由注册完成
2025-07-03 20:07:35.272 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:07:35.272 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:08:14.407 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:08:14.409 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:08:25.821 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:08:26.798 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:08:26.801 FATAL [resource.go:40[Init]] 初始化Redis连接失败: connect to redis cluster failed, ERR unsupported command `cluster`
2025-07-03 20:08:49.472 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:08:50.460 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:08:50.463 FATAL [resource.go:40[Init]] 初始化Redis连接失败: connect to redis cluster failed, ERR unsupported command `cluster`
2025-07-03 20:24:01.935 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:24:02.916 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:24:02.918 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:24:02.920 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:24:02.920 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:24:02.920 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:24:02.920 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:24:02.920 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:24:02.920 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:24:02.922 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:24:02.922 INFO [routes.go:536[RegisterRoutes]] API路由注册完成
2025-07-03 20:24:02.922 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:24:02.922 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:33:59.563 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:33:59.567 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:34:11.762 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:34:12.742 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:34:12.744 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:34:12.746 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:34:12.746 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:34:12.746 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:34:12.746 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:34:12.746 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:34:12.746 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:34:12.747 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:34:12.747 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:34:12.747 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:34:12.747 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:34:12.747 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:34:12.747 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:34:12.747 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:34:12.747 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:34:12.747 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:34:12.748 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:34:12.749 INFO [routes.go:536[RegisterRoutes]] API路由注册完成
2025-07-03 20:34:12.749 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:34:12.749 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:46:35.441 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:46:35.443 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:46:48.840 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:46:49.824 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:46:49.827 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:46:49.833 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:46:49.833 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:46:49.833 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:46:49.833 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:46:49.833 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:46:49.833 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:46:49.835 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:46:49.836 INFO [routes.go:536[RegisterRoutes]] API路由注册完成
2025-07-03 20:46:49.836 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:46:49.836 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:47:00.306 ERROR [handler.go:494[BatchPauseMembers]] BatchPauseMembers ShouldBindJSON error: Key: 'BatchPauseMembersRequest.ProductID' Error:Field validation for 'ProductID' failed on the 'required' tag
2025-07-03 20:49:21.350 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:49:21.353 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:49:35.706 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:49:36.684 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:49:36.687 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:49:36.692 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:49:36.692 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:49:36.692 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:49:36.692 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:49:36.692 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:49:36.692 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:49:36.692 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:49:36.692 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:49:36.693 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:49:36.693 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:49:36.693 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:49:36.693 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:49:36.694 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:49:36.695 INFO [routes.go:536[RegisterRoutes]] API路由注册完成
2025-07-03 20:49:36.695 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:49:36.695 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:57:06.600 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:57:06.603 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 20:57:19.277 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 20:57:20.284 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 20:57:20.287 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 20:57:20.308 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 20:57:20.309 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 20:57:20.309 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 20:57:20.309 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 20:57:20.309 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 20:57:20.309 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 20:57:20.311 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 20:57:20.312 INFO [routes.go:536[RegisterRoutes]] API路由注册完成
2025-07-03 20:57:20.312 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 20:57:20.312 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 20:59:52.202 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 20:59:52.204 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 21:00:05.375 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 21:00:06.370 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 21:00:06.372 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 21:00:06.374 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 21:00:06.374 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 21:00:06.374 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 21:00:06.374 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 21:00:06.374 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 21:00:06.374 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 21:00:06.374 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 21:00:06.374 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 21:00:06.374 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 21:00:06.374 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 21:00:06.374 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 21:00:06.374 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 21:00:06.375 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 21:00:06.375 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 21:00:06.375 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 21:00:06.377 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 21:00:06.377 INFO [routes.go:536[RegisterRoutes]] API路由注册完成
2025-07-03 21:00:06.377 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 21:00:06.378 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 21:02:17.779 ERROR [handler.go:494[BatchPauseMembers]] BatchPauseMembers ShouldBindJSON error: Key: 'BatchPauseMembersRequest.ProductID' Error:Field validation for 'ProductID' failed on the 'required' tag
2025-07-03 21:04:24.230 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 21:04:24.233 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 21:04:42.102 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 21:04:43.081 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 21:04:43.084 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 21:04:43.086 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 21:04:43.086 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 21:04:43.086 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 21:04:43.086 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 21:04:43.086 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 21:04:43.086 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 21:04:43.087 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 21:04:43.087 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 21:04:43.088 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 21:04:43.088 INFO [routes.go:536[RegisterRoutes]] API路由注册完成
2025-07-03 21:04:43.088 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 21:04:43.088 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 21:09:57.642 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 21:09:57.650 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 21:10:11.215 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 21:10:12.205 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 21:10:12.208 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 21:10:12.210 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 21:10:12.210 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 21:10:12.210 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 21:10:12.210 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 21:10:12.210 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 21:10:12.210 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 21:10:12.210 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 21:10:12.210 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 21:10:12.210 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 21:10:12.210 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 21:10:12.210 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 21:10:12.210 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 21:10:12.211 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 21:10:12.211 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 21:10:12.211 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 21:10:12.212 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 21:10:12.213 INFO [routes.go:536[RegisterRoutes]] API路由注册完成
2025-07-03 21:10:12.213 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 21:10:12.213 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 21:10:23.130 ERROR [handler.go:494[BatchPauseMembers]] BatchPauseMembers ShouldBindJSON error: Key: 'BatchPauseMembersRequest.ProductID' Error:Field validation for 'ProductID' failed on the 'required' tag
2025-07-03 21:17:11.522 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 21:17:11.525 INFO [main.go:98[main]] 服务器已关闭
2025-07-03 21:17:24.398 INFO [main.go:50[main]] 服务正在启动...
2025-07-03 21:17:25.384 INFO [resource.go:36[Init]] MongoDB连接成功，数据库: alpha-rm_sase
2025-07-03 21:17:25.386 INFO [resource.go:43[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-07-03 21:17:25.397 INFO [common.go:129[Init]] 初始化模型实例...
2025-07-03 21:17:25.397 INFO [common.go:142[Init]] 办公设置仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:146[Init]] 应用分类仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:150[Init]] 应用标签仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:154[Init]] 门户应用分类仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:158[Init]] 门户应用分类关联仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:162[Init]] 应用基础仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:166[Init]] 应用标签关系仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:170[Init]] 应用管理员仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:183[Init]] 所有模型实例初始化完成
2025-07-03 21:17:25.397 INFO [common.go:187[Init]] 连接器集群仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:191[Init]] 连接器仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:195[Init]] 应用健康状态仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:198[Init]] 策略关联仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:201[Init]] 策略虚拟用户组仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:205[Init]] 策略处置仓库已初始化
2025-07-03 21:17:25.397 INFO [common.go:208[Init]] 所有模型实例初始化完成
2025-07-03 21:17:25.398 INFO [common.go:211[Init]] 所有模型实例初始化完成
2025-07-03 21:17:25.398 INFO [common.go:215[Init]] ZTNA访问日志仓库已初始化
2025-07-03 21:17:25.398 INFO [common.go:218[Init]] 应用权限仓库已初始化
2025-07-03 21:17:25.398 INFO [common.go:222[Init]] 门户应用配置仓库已初始化
2025-07-03 21:17:25.398 INFO [common.go:225[Init]] 通知策略仓库已初始化
2025-07-03 21:17:25.398 INFO [common.go:228[Init]] 通知IM设置仓库已初始化
2025-07-03 21:17:25.398 INFO [common.go:231[Init]] 通知策略范围仓库已初始化
2025-07-03 21:17:25.398 INFO [common.go:234[Init]] 通知消息仓库已初始化
2025-07-03 21:17:25.398 INFO [common.go:237[Init]] 下载渠道仓库已初始化
2025-07-03 21:17:25.398 INFO [common.go:241[Init]] 部署下载包仓库已初始化
2025-07-03 21:17:25.398 INFO [common.go:245[Init]] 企业设置仓库已初始化
2025-07-03 21:17:25.399 INFO [routes.go:50[RegisterRoutes]] 开始注册API路由
2025-07-03 21:17:25.400 INFO [routes.go:536[RegisterRoutes]] API路由注册完成
2025-07-03 21:17:25.401 INFO [main.go:81[main]] 服务器已启动，监听端口: 8687, 模式: debug
2025-07-03 21:17:25.401 INFO [main.go:75[func1]] HTTP服务器监听端口: 8687
2025-07-03 21:17:52.530 INFO [main.go:87[main]] 正在关闭服务器...
2025-07-03 21:17:52.533 INFO [main.go:98[main]] 服务器已关闭
