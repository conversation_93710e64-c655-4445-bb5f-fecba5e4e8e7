package model

import (
	"sase_cloud_api/internal/model/app_admin"
	"sase_cloud_api/internal/model/app_base"
	"sase_cloud_api/internal/model/app_category"
	"sase_cloud_api/internal/model/app_category_tag"
	"sase_cloud_api/internal/model/app_health"
	"sase_cloud_api/internal/model/app_portal_category"
	"sase_cloud_api/internal/model/app_portal_category_relation"
	"sase_cloud_api/internal/model/app_portal_config"
	"sase_cloud_api/internal/model/app_tag_relation"
	"sase_cloud_api/internal/model/certs"
	"sase_cloud_api/internal/model/connector"
	"sase_cloud_api/internal/model/connector_cluster"
	"sase_cloud_api/internal/model/connector_cluster_app_relation"
	"sase_cloud_api/internal/model/department"
	"sase_cloud_api/internal/model/department_member"
	"sase_cloud_api/internal/model/department_role"
	"sase_cloud_api/internal/model/department_role_relation"
	"sase_cloud_api/internal/model/department_user_relation"
	"sase_cloud_api/internal/model/deploy_invite_info"
	"sase_cloud_api/internal/model/device_group"
	"sase_cloud_api/internal/model/ip_config"
	"sase_cloud_api/internal/model/logs_ztna_access"
	"sase_cloud_api/internal/model/notify_im_setting"
	"sase_cloud_api/internal/model/notify_message"
	"sase_cloud_api/internal/model/notify_policy"
	"sase_cloud_api/internal/model/notify_policy_scope"
	"sase_cloud_api/internal/model/organization_role"
	"sase_cloud_api/internal/model/policy"
	"sase_cloud_api/internal/model/policy_disposal"
	"sase_cloud_api/internal/model/policy_network_region"
	"sase_cloud_api/internal/model/policy_process_group"
	"sase_cloud_api/internal/model/policy_role_relation"
	"sase_cloud_api/internal/model/policy_scene_template"
	"sase_cloud_api/internal/model/policy_virtual_user_group"
	"sase_cloud_api/internal/model/scene_template"
	"sase_cloud_api/internal/model/sync_record"
	"sase_cloud_api/internal/model/user"
	"sase_cloud_api/internal/model/user_group"
	"sase_cloud_api/internal/model/user_group_relation"

	"sase_cloud_api/internal/model/apply_permission"
	"sase_cloud_api/internal/model/deploy_download_pkg"
	"sase_cloud_api/internal/model/enterprise_setting"
	"sase_cloud_api/internal/model/org_platform_config"
	"sase_cloud_api/pkg/logger"
)

var (
	DepartmentRepo                  department.Repository
	DepartmentRoleRepo              department_role.Repository
	DepartmentMemberRepo            department_member.Repository
	DepartmentUserRelationRepo      department_user_relation.Repository
	DepartmentRoleRelationRepo      department_role_relation.Repository
	UserGroupRepo                   user_group.Repository
	UserGroupRelationRepo           user_group_relation.Repository
	UserRepo                        user.Repository
	DeviceGroupRepo                 device_group.Repository
	ConnectorClusterAppRelationRepo connector_cluster_app_relation.Repository

	// AppCategoryRepo 应用分类仓库实例
	AppCategoryRepo app_category.Repository
	// AppCategoryTagRepo 应用标签仓库实例
	AppCategoryTagRepo app_category_tag.Repository
	// AppPortalCategoryRepo 门户应用分类仓库实例
	AppPortalCategoryRepo app_portal_category.Repository
	// AppPortalCategoryRelationRepo 门户应用分类关联仓库实例
	AppPortalCategoryRelationRepo app_portal_category_relation.Repository
	OrgPlatformConfigRepo         org_platform_config.Repository
	// AppBaseRepo 应用基础仓库实例
	AppBaseRepo   app_base.Repository
	AppPolicyRepo policy.Repository
	// AppTagRelationRepo 应用标签关系仓库实例
	AppTagRelationRepo app_tag_relation.TagRelationRepository
	// AppAdminRepo 应用管理员仓库实例
	AppAdminRepo app_admin.Repository

	SyncRecord           sync_record.Repository
	OrganizationRoleRepo organization_role.Repository

	CertRepo     certs.Repository
	IPConfigRepo ip_config.Repository

	SceneTemplateRepo       scene_template.Repository
	PolicySceneTemplateRepo policy_scene_template.Repository

	// ConnectorClusterRepo 连接器集群仓库实例
	ConnectorClusterRepo connector_cluster.ConnectorClusterRepository
	// ConnectorRepo 连接器仓库实例
	ConnectorRepo connector.ConnectorRepository

	AppHealthRepo              app_health.Repository
	PolicyRoleRelationRepo     policy_role_relation.Repository
	PolicyVirtualUserGroupRepo policy_virtual_user_group.Repository

	// PolicyDisposalRepo 策略处置仓库实例
	PolicyDisposalRepo policy_disposal.Repository

	PolicyNetworkRegionRepo policy_network_region.Repository

	PolicyProcessGroupRepo policy_process_group.Repository

	// LogsZtnaAccessRepo ZTNA访问日志仓库实例
	LogsZtnaAccessRepo logs_ztna_access.Repository

	ApplyPermissionRepo apply_permission.Repository

	// AppPortalConfigRepo 门户应用配置仓库实例
	AppPortalConfigRepo app_portal_config.Repository

	NotifyPolicyRepo      notify_policy.Repository
	NotifyImSettingRepo   notify_im_setting.Repository
	NotifyPolicyScopeRepo notify_policy_scope.Repository
	NotifyMessageRepo     notify_message.Repository

	DeployInviteInfoRepo deploy_invite_info.Repository

	// DeployDownloadPkgRepo 部署下载包仓库实例
	DeployDownloadPkgRepo deploy_download_pkg.Repository

	// EnterpriseSettingRepo 企业设置仓库实例
	EnterpriseSettingRepo enterprise_setting.Repository
)

// Init 初始化所有模型实例
func Init() {
	logger.Info("初始化模型实例...")

	DepartmentRepo = department.NewRepository()
	DepartmentRoleRepo = department_role.NewRepository()
	DepartmentMemberRepo = department_member.NewRepository()
	DepartmentRoleRelationRepo = department_role_relation.NewRepository()
	DepartmentUserRelationRepo = department_user_relation.NewRepository()
	UserGroupRepo = user_group.NewRepository()
	UserGroupRelationRepo = user_group_relation.NewRepository()
	UserRepo = user.NewRepository()
	DeviceGroupRepo = device_group.NewRepository()
	ConnectorClusterAppRelationRepo = connector_cluster_app_relation.NewRepository()

	logger.Info("办公设置仓库已初始化")

	// 初始化应用分类仓库
	AppCategoryRepo = app_category.NewRepository()
	logger.Info("应用分类仓库已初始化")

	// 初始化应用标签仓库
	AppCategoryTagRepo = app_category_tag.NewRepository()
	logger.Info("应用标签仓库已初始化")

	// 初始化门户应用分类仓库
	AppPortalCategoryRepo = app_portal_category.NewRepository()
	logger.Info("门户应用分类仓库已初始化")

	// 初始化门户应用分类关联仓库
	AppPortalCategoryRelationRepo = app_portal_category_relation.NewRepository()
	logger.Info("门户应用分类关联仓库已初始化")

	// 初始化应用基础仓库
	AppBaseRepo = app_base.NewRepository()
	logger.Info("应用基础仓库已初始化")

	// 初始化应用标签关系仓库
	AppTagRelationRepo = app_tag_relation.NewTagRelationRepository()
	logger.Info("应用标签关系仓库已初始化")

	// 初始化应用管理员仓库
	AppAdminRepo = app_admin.NewRepository()
	logger.Info("应用管理员仓库已初始化")

	OrgPlatformConfigRepo = org_platform_config.NewMongoRepository()

	AppPolicyRepo = policy.NewRepository()

	SyncRecord = sync_record.NewRecordRepository()
	OrganizationRoleRepo = organization_role.NewRepository()
	CertRepo = certs.NewRepository()
	IPConfigRepo = ip_config.NewRepository()

	SceneTemplateRepo = scene_template.NewRepository()
	PolicySceneTemplateRepo = policy_scene_template.NewRepository()
	logger.Info("所有模型实例初始化完成")

	// 初始化连接器集群仓库
	ConnectorClusterRepo = connector_cluster.NewConnectorClusterRepository()
	logger.Info("连接器集群仓库已初始化")

	// 初始化连接器仓库
	ConnectorRepo = connector.NewConnectorRepository()
	logger.Info("连接器仓库已初始化")

	// 初始化应用健康状态仓库
	AppHealthRepo = app_health.NewRepository()
	logger.Info("应用健康状态仓库已初始化")

	PolicyRoleRelationRepo = policy_role_relation.NewRepository()
	logger.Info("策略关联仓库已初始化")

	PolicyVirtualUserGroupRepo = policy_virtual_user_group.NewRepository()
	logger.Info("策略虚拟用户组仓库已初始化")

	// 初始化策略处置仓库
	PolicyDisposalRepo = policy_disposal.NewRepository()
	logger.Info("策略处置仓库已初始化")

	PolicyNetworkRegionRepo = policy_network_region.NewRepository()
	logger.Info("所有模型实例初始化完成")

	PolicyProcessGroupRepo = policy_process_group.NewRepository()
	logger.Info("所有模型实例初始化完成")

	// 初始化ZTNA访问日志仓库
	LogsZtnaAccessRepo = logs_ztna_access.NewRepository()
	logger.Info("ZTNA访问日志仓库已初始化")

	ApplyPermissionRepo = apply_permission.NewRepository()
	logger.Info("应用权限仓库已初始化")

	// 初始化门户应用配置仓库
	AppPortalConfigRepo = app_portal_config.NewRepository()
	logger.Info("门户应用配置仓库已初始化")

	NotifyPolicyRepo = notify_policy.NewRepository()
	logger.Info("通知策略仓库已初始化")

	NotifyImSettingRepo = notify_im_setting.NewRepository()
	logger.Info("通知IM设置仓库已初始化")

	NotifyPolicyScopeRepo = notify_policy_scope.NewRepository()
	logger.Info("通知策略范围仓库已初始化")

	NotifyMessageRepo = notify_message.NewRepository()
	logger.Info("通知消息仓库已初始化")

	DeployInviteInfoRepo = deploy_invite_info.NewRepository()
	logger.Info("下载渠道仓库已初始化")

	// 初始化部署下载包仓库
	DeployDownloadPkgRepo = deploy_download_pkg.NewRepository()
	logger.Info("部署下载包仓库已初始化")

	// 初始化企业设置仓库
	EnterpriseSettingRepo = enterprise_setting.NewRepository()
	logger.Info("企业设置仓库已初始化")
}
