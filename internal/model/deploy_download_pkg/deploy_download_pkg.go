package deploy_download_pkg

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"sase_cloud_api/internal/resource"
	"sase_cloud_api/pkg/logger"
)

// DeployDownloadPkg 部署下载包模型
type DeployDownloadPkg struct {
	ID          primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	ProductId   string             `json:"product_id" bson:"product_id"`
	Name        string             `json:"name" bson:"name"`
	Arch        string             `json:"arch" bson:"arch"`
	CreatedAt   int64              `json:"created_at" bson:"created_at"`
	DownloadUrl string             `json:"download_url" bson:"download_url"`
	FileName    string             `json:"file_name" bson:"file_name"`
	MD5         string             `json:"md5" bson:"md5"`
	Platform    int                `json:"platform" bson:"platform"`
	SHA1        string             `json:"sha1" bson:"sha1"`
	SHA256      string             `json:"sha256" bson:"sha256"`
	Size        int64              `json:"size" bson:"size"`
	UpdatedAt   int64              `json:"updated_at" bson:"updated_at"`
	Version     string             `json:"version" bson:"version"`
}

// Repository 部署下载包仓库接口
type Repository interface {
	// Create 创建部署下载包
	Create(ctx context.Context, pkg *DeployDownloadPkg) error
	// InsertMany 批量创建部署下载包
	InsertMany(ctx context.Context, pkgs []interface{}) error
	// Update 更新部署下载包
	Update(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error)
	// UpdateMany 批量更新部署下载包
	UpdateMany(ctx context.Context, filter interface{}, update interface{}) error
	// Delete 删除部署下载包
	Delete(ctx context.Context, filter interface{}) error
	// DeleteMany 批量删除部署下载包
	DeleteMany(ctx context.Context, filter interface{}) error
	// FindOne 查询单条部署下载包
	FindOne(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*DeployDownloadPkg, error)
	// Find 查询多条部署下载包
	Find(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*DeployDownloadPkg, error)
	// FindByProductId 根据产品ID查询部署下载包
	FindByProductId(ctx context.Context, productId string) ([]*DeployDownloadPkg, error)
	// FindByPlatform 根据平台查询部署下载包
	FindByPlatform(ctx context.Context, platform int) ([]*DeployDownloadPkg, error)
	// FindByProductIdAndPlatform 根据产品ID和平台查询部署下载包
	FindByProductIdAndPlatform(ctx context.Context, productId string, platform int) ([]*DeployDownloadPkg, error)
	// FindByVersion 根据版本查询部署下载包
	FindByVersion(ctx context.Context, version string) ([]*DeployDownloadPkg, error)
	// CountDocuments 统计文档数量
	CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions) (int64, error)
}

type repository struct {
	collection *mongo.Collection
}

// NewRepository 创建部署下载包仓库实例
func NewRepository() Repository {
	collection := resource.Database.Collection("deploy_download_pkg")
	return &repository{collection: collection}
}

// Create 创建部署下载包
func (r *repository) Create(ctx context.Context, pkg *DeployDownloadPkg) error {
	now := time.Now().Unix()
	pkg.CreatedAt = now
	pkg.UpdatedAt = now

	_, err := r.collection.InsertOne(ctx, pkg)
	if err != nil {
		logger.Errorf("创建部署下载包失败: %v", err)
		return err
	}
	return nil
}

// InsertMany 批量创建部署下载包
func (r *repository) InsertMany(ctx context.Context, pkgs []interface{}) error {
	if len(pkgs) == 0 {
		return nil
	}

	_, err := r.collection.InsertMany(ctx, pkgs)
	if err != nil {
		logger.Errorf("批量创建部署下载包失败: %v", err)
		return err
	}
	return nil
}

// Update 更新部署下载包
func (r *repository) Update(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) {
	// 自动更新 updated_at 字段
	updateDoc := bson.M{}
	if updateM, ok := update.(bson.M); ok {
		if set, exists := updateM["$set"]; exists {
			if setM, ok := set.(bson.M); ok {
				setM["updated_at"] = time.Now().Unix()
			}
		} else {
			updateDoc["$set"] = bson.M{
				"updated_at": time.Now().Unix(),
			}
			for k, v := range updateM {
				updateDoc[k] = v
			}
			update = updateDoc
		}
	}

	result, err := r.collection.UpdateOne(ctx, filter, update, opts...)
	if err != nil {
		logger.Errorf("更新部署下载包失败: %v", err)
		return nil, err
	}
	return result, nil
}

// UpdateMany 批量更新部署下载包
func (r *repository) UpdateMany(ctx context.Context, filter interface{}, update interface{}) error {
	// 自动更新 updated_at 字段
	updateDoc := bson.M{}
	if updateM, ok := update.(bson.M); ok {
		if set, exists := updateM["$set"]; exists {
			if setM, ok := set.(bson.M); ok {
				setM["updated_at"] = time.Now().Unix()
			}
		} else {
			updateDoc["$set"] = bson.M{
				"updated_at": time.Now().Unix(),
			}
			for k, v := range updateM {
				updateDoc[k] = v
			}
			update = updateDoc
		}
	}

	_, err := r.collection.UpdateMany(ctx, filter, update)
	if err != nil {
		logger.Errorf("批量更新部署下载包失败: %v", err)
		return err
	}
	return nil
}

// Delete 删除部署下载包
func (r *repository) Delete(ctx context.Context, filter interface{}) error {
	_, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		logger.Errorf("删除部署下载包失败: %v", err)
		return err
	}
	return nil
}

// DeleteMany 批量删除部署下载包
func (r *repository) DeleteMany(ctx context.Context, filter interface{}) error {
	_, err := r.collection.DeleteMany(ctx, filter)
	if err != nil {
		logger.Errorf("批量删除部署下载包失败: %v", err)
		return err
	}
	return nil
}

// FindOne 查询单条部署下载包
func (r *repository) FindOne(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*DeployDownloadPkg, error) {
	result := &DeployDownloadPkg{}
	err := r.collection.FindOne(ctx, filter, opts...).Decode(result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		logger.Errorf("查询部署下载包失败: %v", err)
		return nil, err
	}
	return result, nil
}

// Find 查询多条部署下载包
func (r *repository) Find(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*DeployDownloadPkg, error) {
	cursor, err := r.collection.Find(ctx, filter, opts...)
	if err != nil {
		logger.Errorf("查询部署下载包列表失败: %v", err)
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []*DeployDownloadPkg
	if err = cursor.All(ctx, &results); err != nil {
		logger.Errorf("解析部署下载包数据失败: %v", err)
		return nil, err
	}
	return results, nil
}

// FindByProductId 根据产品ID查询部署下载包
func (r *repository) FindByProductId(ctx context.Context, productId string) ([]*DeployDownloadPkg, error) {
	filter := bson.M{"product_id": productId}
	return r.Find(ctx, filter)
}

// FindByPlatform 根据平台查询部署下载包
func (r *repository) FindByPlatform(ctx context.Context, platform int) ([]*DeployDownloadPkg, error) {
	filter := bson.M{"platform": platform}
	return r.Find(ctx, filter)
}

// FindByProductIdAndPlatform 根据产品ID和平台查询部署下载包
func (r *repository) FindByProductIdAndPlatform(ctx context.Context, productId string, platform int) ([]*DeployDownloadPkg, error) {
	filter := bson.M{
		"product_id": productId,
		"platform":   platform,
	}
	return r.Find(ctx, filter)
}

// FindByVersion 根据版本查询部署下载包
func (r *repository) FindByVersion(ctx context.Context, version string) ([]*DeployDownloadPkg, error) {
	filter := bson.M{"version": version}
	return r.Find(ctx, filter)
}

// CountDocuments 统计文档数量
func (r *repository) CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, filter, opts...)
	if err != nil {
		logger.Errorf("统计部署下载包数量失败: %v", err)
		return 0, err
	}
	return count, nil
}
