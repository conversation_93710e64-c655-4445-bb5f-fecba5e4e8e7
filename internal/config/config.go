package config

import (
	"flag"
	"fmt"
	"time"

	"github.com/spf13/viper"
	"rm.git/client_api/rm_common_libs.git/v2/common/configs"
)

var (
	NginxMinioBucket = "minio-webhook-sync"
)

// Configuration 全局配置结构体
type Configuration struct {
	Server       ServerConfig        `mapstructure:"server"`
	MongoDB      MongoDBConfig       `mapstructure:"mongodb"`
	Redis        RedisConfig         `mapstructure:"redis"`
	Log          LogConfig           `mapstructure:"log"`
	NginxConfTpl NginxConfTpl        `mapstructure:"nginx_conf_tpl"`
	NginxMinio   configs.MinioConfig `mapstructure:"nginx_minio"`
	ThirdParty   Address             `mapstructure:"third_party"`
	Email        EmailConfig         `mapstructure:"email"`
}
type Address struct {
	Address string `mapstructure:"address"`
}

type NginxConfTpl struct {
	WatermarkPath string `mapstructure:"watermark_path"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port            int    `mapstructure:"port"`
	Mode            string `mapstructure:"mode"`
	MaxExportLimit  int64  `mapstructure:"max_export_limit"`
	Context         string `mapstructure:"context"`
	DeployInviteUrl string `mapstructure:"deploy_invite_url"`
	PkgDownloadUrl  string `mapstructure:"pkg_download_url"`
}

// MongoDBConfig MongoDB配置
type MongoDBConfig struct {
	URI        string        `mapstructure:"uri"`
	Database   string        `mapstructure:"database"`
	Username   string        `mapstructure:"username"`
	Password   string        `mapstructure:"password"`
	Address    string        `mapstructure:"addr"`
	Direct     bool          `mapstructure:"direct"`
	ReplicaSet string        `mapstructure:"replica_set"`
	Options    string        `mapstructure:"options"`
	Timeout    time.Duration `mapstructure:"timeout"`
}

type EmailConfig struct {
	Port      int    `mapstructure:"port"`
	Host      string `mapstructure:"host"`
	User      string `mapstructure:"user"`
	Password  string `mapstructure:"password"`
	AliasName string `mapstructure:"alias_name"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Addr           []string `mapstructure:"addr"`
	Password       string   `mapstructure:"password"`
	DB             int      `mapstructure:"db"`
	ConnectionMode int      `mapstructure:"connection_mode"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level string `mapstructure:"level"`
	Path  string `mapstructure:"path"`
}

var config Configuration

// Init 初始化配置
func Init() error {
	// 设置配置文件路径
	configPath := ""
	flag.StringVar(&configPath, "c", "./configs", "config path")
	flag.Parse()
	viper.AddConfigPath(configPath)
	viper.SetConfigName("config")
	viper.SetConfigType("toml")

	// 读取环境变量
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置到结构体
	if err := viper.Unmarshal(&config); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}

	return nil
}

// Get 获取配置
func Get() Configuration {
	return config
}
