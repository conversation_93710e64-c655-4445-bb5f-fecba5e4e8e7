package dto

import (
	"errors"
)

// CreateUserGroupRequest 创建用户组请求
type CreateUserGroupRequest struct {
	ProductID        string      `json:"product_id" `
	GroupName        string      `json:"group_name" validate:"required"`
	Conditions       []Condition `json:"conditions" validate:"required"`
	InvalidUserCodes []string    `json:"invalid_user_codes"`
}

// UpdateUserGroupRequest 更新用户组请求
type UpdateUserGroupRequest struct {
	ProductID        string      `json:"product_id" `
	GroupCode        string      `json:"group_code" validate:"required"`
	GroupName        string      `json:"group_name" validate:"required"`
	Conditions       []Condition `json:"conditions" validate:"required"`
	InvalidUserCodes []string    `json:"invalid_user_codes"`
}

// DeleteUserGroupRequest 删除用户组请求
type DeleteUserGroupRequest struct {
	ProductID string `json:"product_id" `
	GroupCode string `json:"group_code" validate:"required"`
}

// SearchUserGroupRequest 搜索用户组请求
type SearchUserGroupRequest struct {
	ProductID string `json:"product_id" binding:"required"`
	GroupName string `json:"group_name"`
	Page      int    `json:"page" validate:"required,min=1"`
	Limit     int    `json:"limit" validate:"required,min=1,max=100"`
}

// SearchUserGroupByCodesRequest 根据用户组编码搜索用户组请求
type SearchUserGroupByCodesRequest struct {
	ProductID  string   `json:"product_id" binding:"required"`
	GroupCodes []string `json:"group_codes" validate:"required"`
}

// SearchUserGroupByCodesResponse 根据用户组编码搜索用户组响应
type SearchUserGroupByCodesResponse struct {
	Total int                 `json:"total"`
	Items []UserGroupResponse `json:"items"`
}

// GetUserGroupByUserCodeResponse 根据用户编码搜索用户组响应
type GetUserGroupByUserCodeResponse struct {
	Total int      `json:"total"`
	Items []string `json:"items"`
}

// GetUserGroupDetailRequest 获取用户组详情请求
type GetUserGroupDetailRequest struct {
	ProductID string `json:"product_id" binding:"required"`
	GroupCode string `json:"group_code" validate:"required"`
}

type GetUserGroupDetailResponse struct {
	GroupCode       string            `json:"group_code"`
	GroupName       string            `json:"group_name"`
	Conditions      []Condition       `json:"conditions"`
	InvalidUserInfo []InvalidUserInfo `json:"invalid_user_codes"`
}

type InvalidUserInfo struct {
	UserCode string       `json:"user_code"`
	UserName string       `json:"user_name"`
	OrgCode  string       `json:"org_code"`
	OrgName  string       `json:"org_name"`
	DeptPath [][]DeptPath `json:"dept_path"`
}

// GetUserGroupByUserCodeRequest 根据用户编码搜索用户组请求
type GetUserGroupByUserCodeRequest struct {
	ProductID string `json:"product_id" binding:"required"`
	UserCode  string `json:"user_code" validate:"required"`
}

func (r *GetUserGroupByUserCodeRequest) Validate() error {
	if r.ProductID == "" {
		return errors.New("产品ID不能为空")
	}
	if r.UserCode == "" {
		return errors.New("用户编码不能为空")
	}
	return nil
}

// GetUserGroupUserInfoRequest 获取用户组用户信息请求
type GetUserGroupUserInfoRequest struct {
	ProductID string `json:"product_id" binding:"required"`
	UserCode  string `json:"user_code" validate:"required"`
	GroupCode string `json:"group_code" validate:"required"`
}

// UserGroupResponse 用户组响应
type UserGroupResponse struct {
	GroupCode        string      `json:"group_code"`
	GroupName        string      `json:"group_name"`
	Conditions       []Condition `json:"conditions"`
	InvalidUserCodes []string    `json:"invalid_user_codes"`
}

// SearchUserGroupResponse 搜索用户组响应
type SearchUserGroupResponse struct {
	Total int                 `json:"total"`
	Items []UserGroupResponse `json:"items"`
}

type BatchUserGroupExportRequest struct {
	GroupCodes []string `json:"group_codes" binding:"required"`
}

// Validate 验证删除用户组请求
func (r *DeleteUserGroupRequest) Validate() error {
	if r.ProductID == "" {
		return errors.New("产品ID不能为空")
	}
	if r.GroupCode == "" {
		return errors.New("用户组编码不能为空")
	}
	return nil
}

// Validate 验证获取用户组详情请求
func (r *GetUserGroupDetailRequest) Validate() error {
	if r.ProductID == "" {
		return errors.New("产品ID不能为空")
	}
	if r.GroupCode == "" {
		return errors.New("用户组编码不能为空")
	}
	return nil
}

// Validate 验证获取用户组用户信息请求
func (r *GetUserGroupUserInfoRequest) Validate() error {
	if r.ProductID == "" {
		return errors.New("产品ID不能为空")
	}
	if r.UserCode == "" {
		return errors.New("用户编码不能为空")
	}
	if r.GroupCode == "" {
		return errors.New("用户组编码不能为空")
	}
	return nil
}
