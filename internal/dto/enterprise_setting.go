package dto

import (
	"errors"
	"sase_cloud_api/internal/common/consts"
	"strings"
)

// EnterpriseSettingCreateRequest 创建企业设置请求
type EnterpriseSettingCreateRequest struct {
	Contents string `json:"contents" binding:"required"` // 设置内容，JSON字符串格式
	Type     string `json:"type" binding:"required"`     // 设置类型
}

// EnterpriseSettingUpdateRequest 更新企业设置请求
type EnterpriseSettingUpdateRequest struct {
	Contents string `json:"contents" binding:"required"` // 设置内容，JSON字符串格式
	Type     string `json:"type" binding:"required"`     // 设置类型
}

// EnterpriseSettingQueryRequest 查询企业设置请求
type EnterpriseSettingQueryRequest struct {
	Type string `form:"type"` // 设置类型，可选
}

// EnterpriseSettingResponse 企业设置响应
type EnterpriseSettingResponse struct {
	ProductId string `json:"product_id"` // 产品ID
	Contents  string `json:"contents"`   // 设置内容
	Type      string `json:"type"`       // 设置类型
}

// ValidateEnterpriseSettingCreateRequest 验证创建企业设置请求
func ValidateEnterpriseSettingCreateRequest(req *EnterpriseSettingCreateRequest) error {
	if strings.TrimSpace(req.Contents) == "" {
		return errors.New("设置内容不能为空")
	}

	if strings.TrimSpace(req.Type) == "" {
		return errors.New("设置类型不能为空")
	}

	// 验证类型的有效性（可根据业务需求扩展）
	validTypes := map[string]bool{
		consts.JoinCompany:     true,
		consts.AccountSecurity: true,
	}

	if !validTypes[req.Type] {
		return errors.New("无效的设置类型")
	}

	return nil
}

// ValidateEnterpriseSettingUpdateRequest 验证更新企业设置请求
func ValidateEnterpriseSettingUpdateRequest(req *EnterpriseSettingUpdateRequest) error {
	if strings.TrimSpace(req.Contents) == "" {
		return errors.New("设置内容不能为空")
	}

	if strings.TrimSpace(req.Type) == "" {
		return errors.New("设置类型不能为空")
	}

	// 验证类型的有效性（可根据业务需求扩展）
	validTypes := map[string]bool{
		consts.AccountSecurity: true,
		consts.JoinCompany:     true,
		// 可以添加其他有效类型
	}

	if !validTypes[req.Type] {
		return errors.New("无效的设置类型")
	}

	return nil
}
