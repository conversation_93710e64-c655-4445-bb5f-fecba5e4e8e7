package consts

const (
	UserInviteSendPasswordEmailTemplateZh = `
<div>
<p>Hi，{{username}}  欢迎您使用 {{productName}}</p>
<p>您可以使用以下链接，下载客户端  {{link}}</p>
<p>您可以使用以下账户，登录平台 您的账号名: {{account}} 账户初始密码：{{password}}</p>
</div>
`
	UserInviteSendPasswordEmailTemplateZh1 = `
<div>
<p>Hi，{{username}}  欢迎您使用 {{productName}}</p>
<p>您可以使用以下链接，下载客户端  {{link}}</p>
<p>您可以使用以下账户，登录平台 您的账号名: {{account}} </p>
</div>
`

	UserInviteSendPasswordEmailTemplateEn = `
<div>
<p>Hi, {{username}}: Welcome to use {{productName}}.</p>
<p> You can use the following link to download the client！ {{link}}</p>
<p>You can use the following account to log in to the platform.  Your account name is {{account}} and your initial password is {{password}}</p>
</div>
`
	UserInviteSendPasswordEmailTemplateEn1 = `
<div>
<p>Hi, {{username}}: Welcome to use {{productName}}.</p>
<p> You can use the following link to download the client！ {{link}}</p>
<p>You can use the following account to log in to the platform.  Your account name is {{account}} </p>
</div>
`

	UserInviteSendLinkEmailTemplateZh = `
<div>
<p>Hi，{{username}}  欢迎您使用 {{productName}}</p>
<p>您可以使用以下链接，下载客户端  {{link}}</p>
</div>
`

	UserInviteSendLinkEmailTemplateEn = `
<div>
<p>Hi, {{username}}: Welcome to use {{productName}}.</p>
<p> You can use the following link to download the client！ {{link}}</p>
</div>
`
)

var EmailSubjectConf = map[string]map[string]string{
	LanguageZh: {
		"subject":     "自定义组织员工邀请",
		"productName": "产品名称",
	},
	LanguageEn: {
		"subject":     "Customize organization employee invitations",
		"productName": "Product Name",
	},
}

var UserInviteSendEmailTemplate = map[int]map[string]map[string]string{
	SendEmailBusinessType1: {
		LanguageZh: {
			SourceTypeCustom: UserInviteSendPasswordEmailTemplateZh,
			"default":        UserInviteSendPasswordEmailTemplateZh1,
		},
		LanguageEn: {
			SourceTypeCustom: UserInviteSendPasswordEmailTemplateEn,
			"default":        UserInviteSendPasswordEmailTemplateEn1,
		},
	},
	SendEmailBusinessType2: {
		LanguageZh: {
			"default": UserInviteSendLinkEmailTemplateZh,
		},
		LanguageEn: {
			"default": UserInviteSendLinkEmailTemplateEn,
		},
	},
	SendEmailBusinessType3: {
		LanguageZh: {
			SourceTypeCustom: UserInviteSendPasswordEmailTemplateZh,
			"default":        UserInviteSendPasswordEmailTemplateZh1,
		},
		LanguageEn: {
			SourceTypeCustom: UserInviteSendPasswordEmailTemplateEn,
			"default":        UserInviteSendPasswordEmailTemplateEn1,
		},
	},
}
