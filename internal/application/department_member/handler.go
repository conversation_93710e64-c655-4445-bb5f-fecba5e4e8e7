package department_member

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"

	"sase_cloud_api/internal/common"
	"sase_cloud_api/internal/common/consts"
	"sase_cloud_api/internal/common/errors"
	"sase_cloud_api/internal/config"
	"sase_cloud_api/internal/dto"
	"sase_cloud_api/internal/model"
	"sase_cloud_api/internal/model/department"
	"sase_cloud_api/internal/model/department_user_relation"
	"sase_cloud_api/internal/model/user"
	"sase_cloud_api/internal/model/user_group_relation"
	"sase_cloud_api/internal/resource"
	"sase_cloud_api/internal/util"
	"sase_cloud_api/pkg/logger"
)

type Handler struct {
}

func NewHandler() *Handler {
	return &Handler{}
}

// checkAccountUnique 检查账号在同一个productId下是否唯一
func (h *Handler) checkAccountUnique(ctx context.Context, productID, orgCode, account, excludeUserCode string) (bool, error) {
	searchParams := &user.SearchUserRequest{
		ProductID: productID,
		OrgCode:   orgCode,
		Account:   account,
		Status:    1,
	}

	members, num, err := model.UserRepo.SearchUser(ctx, searchParams)
	if err != nil {
		return false, err
	}

	if num == 0 { // 不存在
		return true, nil
	}

	if num == 1 { // 存在一个, 是当前用户
		if members[0].UserCode == excludeUserCode {
			return true, nil
		}
	}

	return false, nil
}

// CreateMember 创建用户
func (h *Handler) CreateMember(c *gin.Context) {
	var req dto.CreateDepartmentMemberRequest
	req.ProductID = c.GetString("product_id")
	req.DeptCode = c.Param("dept_code")
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("CreateMember ShouldBindJSON error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 检查账号唯一性
	unique, err := h.checkAccountUnique(c.Request.Context(), req.ProductID, req.OrgCode, req.Account, "")
	if err != nil {
		logger.Errorf("CreateMember checkAccountUnique error: %v", err)
		common.ResponseResult(c, errors.AddError)
		return
	}

	if !unique {
		common.ResponseResult(c, errors.AccountAlreadyExists)
		return
	}

	// 构造部门路径
	deptPath := make([][]string, 0) //兼容三方接入组织的一个用户有多个部门的情况，采用二维数组存储部门路径
	path, err := model.DepartmentRepo.GetDepartmentFullPath(c.Request.Context(), req.ProductID, req.OrgCode, req.DeptCode)
	if err != nil {
		logger.Errorf("CreateMember GetDepartmentFullPath error: %v", err)
		common.ResponseResult(c, errors.AddError)
		return
	}
	deptPath = append(deptPath, path)

	// 创建用户
	createUser := &user.CreateUserRequest{
		ProductID:        req.ProductID,
		OrgCode:          req.OrgCode,
		SourceType:       req.SourceType,
		UserCode:         generateUserCode(),
		UserName:         req.UserName,
		Account:          req.Account,
		Mobile:           req.Mobile,
		Email:            req.Email,
		Position:         req.Position,
		Password:         req.Password,
		ValidTime:        req.ValidTime,
		Status:           0,
		BindDeviceStatus: 0,
		DeptPath:         deptPath,
		IsActive:         false,
	}
	if req.Password != "" {
		createUser.Password, err = util.HashPassword(req.Password)
		if err != nil {
			logger.Errorf("CreateMember HashPassword error: %v", err)
			common.ResponseResult(c, errors.AddError)
			return
		}
	}

	if err := model.UserRepo.CreateUser(c.Request.Context(), createUser); err != nil {
		logger.Errorf("CreateMember CreateUser error: %v", err)
		common.ResponseResult(c, errors.AddError)
		return
	}

	// 创建部门成员
	createDepartmentUserRelation := &department_user_relation.DepartmentUserRelation{
		ProductID: req.ProductID,
		OrgCode:   req.OrgCode,
		UserCode:  createUser.UserCode,
		DeptCode:  req.DeptCode,
		DeptPath:  deptPath,
	}
	if err := model.DepartmentUserRelationRepo.CreateDepartmentUserRelation(c.Request.Context(), createDepartmentUserRelation); err != nil {
		logger.Errorf("CreateMember CreateDepartmentUserRelation error: %v", err)
		common.ResponseResult(c, errors.AddError)
		return
	}

	// 创建用户组关系
	if len(req.GroupCodes) > 0 {
		relations := make([]*user_group_relation.UserGroupRelationCreate, 0, len(req.GroupCodes))
		for _, groupCode := range req.GroupCodes {
			relation := &user_group_relation.UserGroupRelationCreate{
				ProductID: req.ProductID,
				UserCode:  createUser.UserCode,
				GroupCode: groupCode,
				AddType:   "1",
				CreatedAt: time.Now().Unix(),
				UpdatedAt: time.Now().Unix(),
			}
			relations = append(relations, relation)
		}
		err = model.UserGroupRelationRepo.BatchCreateRelation(c.Request.Context(), relations)
		if err != nil {
			logger.Errorf("CreateMember BatchCreateRelation error: %v", err)
			common.ResponseResult(c, errors.AddError)
			return
		}
	}

	userDetail, err := model.UserRepo.GetUserDetail(c.Request.Context(), &user.GetUserDetailRequest{
		ProductID: req.ProductID,
		UserCode:  createUser.UserCode,
		OrgCode:   req.OrgCode,
	})

	deployInviteInfo, err := model.DeployInviteInfoRepo.FindOne(c.Request.Context(), bson.M{"product_id": req.ProductID, "expired_time": bson.M{"$gt": time.Now().Unix()}})

	if err != nil {
		logger.Errorf("GetDeployInfo error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	link := ""
	if deployInviteInfo != nil && deployInviteInfo.Code != "" {
		link = fmt.Sprintf(config.Get().Server.DeployInviteUrl, deployInviteInfo.Code)
	}

	// 发送激活邮件
	if req.IsActivationEmailSent {
		_, _ = model.UserRepo.SendEmail(c, consts.SendEmailBusinessType3, []*user.SendEmailStruct{
			{
				Link:       link,
				UserName:   createUser.UserName,
				Account:    createUser.Account,
				Password:   req.Password,
				Email:      createUser.Email,
				SourceType: createUser.SourceType,
			},
		})
	}

	common.ResponseResult(c, convertToResponse(req.ProductID, userDetail, req.GroupCodes, req.DeptCode))
}

// UpdateMember 更新用户
func (h *Handler) UpdateMember(c *gin.Context) {
	var req dto.UpdateDepartmentMemberRequest
	req.ProductID = c.GetString("product_id")
	req.OriginalDeptCode = c.Param("dept_code")
	req.UserCode = c.Param("user_code")
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("UpdateMember ShouldBindJSON error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 检查账号唯一性
	unique, err := h.checkAccountUnique(c.Request.Context(), req.ProductID, req.OrgCode, req.Account, req.UserCode)
	if err != nil {
		logger.Errorf("UpdateMember checkAccountUnique error: %v", err)
		common.ResponseResult(c, errors.AddError)
		return
	}

	if !unique {
		common.ResponseResult(c, errors.AccountAlreadyExists)
		return
	}
	// 如果部门编码为空，则使用原始部门编码
	if req.DeptCode == "" {
		req.DeptCode = req.OriginalDeptCode
	}
	// 构造部门路径
	deptPath := make([][]string, 0) //兼容三方接入组织的一个用户有多个部门的情况，采用二维数组存储部门路径
	path, err := model.DepartmentRepo.GetDepartmentFullPath(c.Request.Context(), req.ProductID, req.OrgCode, req.DeptCode)
	if err != nil {
		logger.Errorf("UpdateMember GetDepartmentFullPath error: %v", err)
		common.ResponseResult(c, errors.AddError)
		return
	}
	deptPath = append(deptPath, path)
	// 更新用户
	updateUser := &user.UpdateUserRequest{
		ProductID: req.ProductID,
		UserCode:  req.UserCode,
		UserName:  req.UserName,
		Account:   req.Account,
		Mobile:    req.Mobile,
		Email:     req.Email,
		Position:  req.Position,
		Password:  req.Password,
		ValidTime: req.ValidTime,
		DeptPath:  deptPath,
	}
	if err := model.UserRepo.UpdateUser(c.Request.Context(), updateUser); err != nil {
		logger.Errorf("UpdateMember UpdateUser error: %v", err)
		common.ResponseResult(c, errors.UpdateError)
		return
	}

	// 更新部门成员
	updateDepartmentUserRelation := &department_user_relation.DepartmentUserRelation{
		ProductID: req.ProductID,
		UserCode:  req.UserCode,
		OrgCode:   req.OrgCode,
		DeptCode:  req.DeptCode,
		DeptPath:  deptPath,
	}
	if err := model.DepartmentUserRelationRepo.UpdateDepartmentUserRelation(c.Request.Context(), req.OriginalDeptCode, updateDepartmentUserRelation); err != nil {
		logger.Errorf("UpdateMember UpdateDepartmentUserRelation error: %v", err)
		common.ResponseResult(c, errors.UpdateError)
		return
	}

	// 4. 删除用户组关系
	err = model.UserGroupRelationRepo.DeleteRelation(c.Request.Context(), req.ProductID, req.UserCode, "")
	if err != nil {
		logger.Errorf("UpdateMember DeleteRelation error: %v", err)
		common.ResponseResult(c, errors.DeleteError)
		return
	}
	// 创建用户组关系
	if len(req.GroupCodes) > 0 {
		relations := make([]*user_group_relation.UserGroupRelationCreate, 0, len(req.GroupCodes))
		for _, groupCode := range req.GroupCodes {
			relation := &user_group_relation.UserGroupRelationCreate{
				ProductID: req.ProductID,
				UserCode:  req.UserCode,
				GroupCode: groupCode,
				AddType:   "1",
				CreatedAt: time.Now().Unix(),
				UpdatedAt: time.Now().Unix(),
			}
			relations = append(relations, relation)
		}
		err = model.UserGroupRelationRepo.BatchCreateRelation(c.Request.Context(), relations)
		if err != nil {
			logger.Errorf("UpdateMember BatchCreateRelation error: %v", err)
			common.ResponseResult(c, errors.AddError)
			return
		}
	}

	userDetail, err := model.UserRepo.GetUserDetail(c.Request.Context(), &user.GetUserDetailRequest{
		ProductID: req.ProductID,
		UserCode:  req.UserCode,
		OrgCode:   req.OrgCode,
	})

	common.ResponseResult(c, convertToResponse(req.ProductID, userDetail, req.GroupCodes, req.DeptCode))
}

// DeleteMember 删除用户
func (h *Handler) DeleteMember(c *gin.Context) {
	var req dto.DeleteDepartmentMemberRequest
	req.ProductID = c.GetString("product_id")
	req.UserCode = c.Param("user_code")
	req.DeptCode = c.Param("dept_code")

	if err := req.Validate(); err != nil {
		logger.Errorf("DeleteMember Validate error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}
	//删除用户
	if err := model.UserRepo.DeleteUser(c.Request.Context(), &user.DeleteUserRequest{
		ProductID: req.ProductID,
		UserCode:  req.UserCode,
	}); err != nil {
		logger.Errorf("DeleteMember DeleteUser error: %v", err)
		common.ResponseResult(c, errors.DeleteError)
		return
	}
	// 删除部门成员
	if err := model.DepartmentUserRelationRepo.DeleteDepartmentUserRelation(c.Request.Context(), &department_user_relation.DepartmentUserRelation{
		ProductID: req.ProductID,
		UserCode:  req.UserCode,
		DeptCode:  req.DeptCode,
	}); err != nil {
		logger.Errorf("DeleteMember error: %v", err)
		common.ResponseResult(c, errors.DeleteError)
		return
	}
	//todo 同步删除member_role_relation

	// 删除所有用户组关系
	if err := model.UserGroupRelationRepo.DeleteRelation(c.Request.Context(), req.ProductID, req.UserCode, ""); err != nil {
		logger.Errorf("DeleteMember DeleteRelation error: %v", err)
		common.ResponseResult(c, errors.DeleteError)
		return
	}

	common.ResponseResult(c, nil)
}

// 批量变更用户部门
func (h *Handler) BatchChangeMemberDept(c *gin.Context) {
	var req dto.BatchChangeMemberDeptRequest
	req.ProductID = c.GetString("product_id")
	req.OriginalDeptCode = c.Param("dept_code")
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("BatchChangeMemberDept ShouldBindJSON error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	if req.OriginalDeptCode == req.DeptCode {
		common.ResponseResult(c, nil)
		return
	}

	// 构造部门路径
	deptPath := make([][]string, 0) //兼容三方接入组织的一个用户有多个部门的情况，采用二维数组存储部门路径
	path, err := model.DepartmentRepo.GetDepartmentFullPath(c.Request.Context(), req.ProductID, req.OrgCode, req.DeptCode)
	if err != nil {
		logger.Errorf("BatchChangeMemberDept GetDepartmentFullPath error: %v", err)
		common.ResponseResult(c, errors.AddError)
		return
	}
	deptPath = append(deptPath, path)

	// 批量更新部门成员关系
	batchUpdateDepartmentUserRelation := &department_user_relation.BatchUpdateDepartmentUserRelationRequest{
		ProductID:        req.ProductID,
		UserCodes:        req.UserCodes,
		OrgCode:          req.OrgCode,
		DeptCode:         req.DeptCode,
		OriginalDeptCode: req.OriginalDeptCode,
		DeptPath:         deptPath,
	}
	if err := model.DepartmentUserRelationRepo.BatchUpdateDepartmentUserRelation(c.Request.Context(), batchUpdateDepartmentUserRelation); err != nil {
		logger.Errorf("BatchChangeMemberDept BatchUpdateDepartmentUserRelation error: %v", err)
		common.ResponseResult(c, errors.UpdateError)
		return
	}

	// 批量更新用户信息
	batchUpdateUser := &user.BatchUpdateUserDeptPathRequest{
		ProductID: req.ProductID,
		OrgCode:   req.OrgCode,
		UserCodes: req.UserCodes,
		DeptPath:  deptPath,
	}
	if err := model.UserRepo.BatchUpdateUserDeptPath(c.Request.Context(), batchUpdateUser); err != nil {
		logger.Errorf("BatchChangeMemberDept BatchUpdateUserDeptPath error: %v", err)
		common.ResponseResult(c, errors.UpdateError)
		return
	}

	common.ResponseResult(c, nil)
}

// BatchInviteMembers 批量邀请
func (h *Handler) BatchInviteMembers(c *gin.Context) {
	var req dto.BatchInviteMembersRequest
	req.ProductID = c.GetString("product_id")
	req.DeptCode = c.Param("dept_code")
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("BatchInviteMembers ShouldBindJSON error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	users, err := model.UserRepo.FindMany(c.Request.Context(), bson.M{"product_id": req.ProductID, "user_code": bson.M{"$in": req.UserCodes}}, nil)

	if err != nil {
		logger.Errorf("BatchInviteMembers FindMany error: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}

	sendEmail := make([]*user.SendEmailStruct, 0, len(req.UserCodes))

	deployInviteInfo, err := model.DeployInviteInfoRepo.FindOne(c.Request.Context(), bson.M{"product_id": req.ProductID, "expired_time": bson.M{"$gt": time.Now().Unix()}})

	if err != nil {
		logger.Errorf("GetDeployInfo error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	link := ""
	if deployInviteInfo != nil && deployInviteInfo.Code != "" {
		link = fmt.Sprintf(config.Get().Server.DeployInviteUrl, deployInviteInfo.Code)
	}

	for _, item := range users {
		var userPassword string

		if item.SourceType == consts.SourceTypeCustom {
			userPassword, _ = util.GeneratePassword(16)

			hashPassword, _ := util.HashPassword(userPassword)

			// 更新用户密码
			filter := bson.M{"user_code": item.UserCode}
			update := bson.M{"$set": bson.M{"password": hashPassword, "updated_at": time.Now().Unix()}}
			if err := model.UserRepo.UpdateOne(c.Request.Context(), filter, update); err != nil {
				logger.Errorf("BatchInviteMembers UpdateOne error: %v", err)
				common.ResponseResult(c, errors.UpdateError)
				return
			}
		}

		if item.Email != "" {
			sendEmail = append(sendEmail, &user.SendEmailStruct{
				Link:       link,
				UserName:   item.UserName,
				Email:      item.Email,
				Account:    item.Account,
				Password:   userPassword,
				SourceType: item.SourceType,
			})
		}
	}

	emailFailCount, err := model.UserRepo.SendEmail(c, req.BusinessType, sendEmail)
	if err != nil {
		logger.Errorf("BatchInviteMembers SendEmail error: %v", err)
		common.ResponseResult(c, errors.SendEmailError)
		return
	}

	common.ResponseResult(c, map[string]interface{}{
		"email_fail_count": emailFailCount,
	})
}

// 批量暂停/恢复
func (h *Handler) BatchPauseMembers(c *gin.Context) {
	var req dto.BatchPauseMembersRequest
	req.ProductID = c.GetString("product_id")
	req.DeptCode = c.Param("dept_code")
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("BatchPauseMembers ShouldBindJSON error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	var userStatus int64
	if req.Switch == "on" {
		userStatus = 1
	} else {
		userStatus = 0
	}
	// 批量更新用户状态
	batchUpdateUser := &user.BatchUpdateUserStatusRequest{
		ProductID: req.ProductID,
		OrgCode:   req.OrgCode,
		UserCodes: req.UserCodes,
		Status:    userStatus,
	}
	if err := model.UserRepo.BatchUpdateUserStatus(c.Request.Context(), batchUpdateUser); err != nil {
		logger.Errorf("BatchPauseMembers BatchUpdateUserStatus error: %v", err)
		common.ResponseResult(c, errors.UpdateError)
		return
	}

	// 禁用删除redis中的token
	if req.Switch == "on" {
		users, _ := model.UserRepo.FindMany(c.Request.Context(), bson.M{"product_id": req.ProductID, "org_code": req.OrgCode, "user_code": bson.M{"$in": req.UserCodes}}, nil)

		for _, item := range users {
			var ctx = context.Background()
			accessTokenKey := consts.AccessTokenPrefix + item.ProductID + "_" + item.UserCode + ":"
			RefreshTokenKey := consts.RefreshTokenPrefix + item.ProductID + "_" + item.UserCode + ":"

			uuids := uuid.New().String()
			resource.Redisclient.Set(ctx, accessTokenKey+uuids, "valid", 24*time.Hour)
			resource.Redisclient.Set(ctx, RefreshTokenKey+uuids, "valid", 24*time.Hour)

			//go func() {
			//	// 删除access token
			//	iter := resource.Redisclient.Scan(ctx, 0, accessTokenKey, 0).Iterator()
			//	for iter.Next(ctx) {
			//		if err := resource.Redisclient.Del(ctx, iter.Val()); err != nil {
			//			logger.Errorf("1 BatchPauseMembers delete access token error: %v", err)
			//		}
			//	}
			//	if err := iter.Err(); err != nil {
			//		logger.Errorf("2 BatchPauseMembers delete access token error: %v", err)
			//	}
			//}()
			//
			go func() {
				// 删除refresh token
				iter := resource.Redisclient.Scan(ctx, 0, RefreshTokenKey, 0).Iterator()
				if err := resource.Redisclient.Del(ctx, iter.Val()); err != nil {
					logger.Errorf("3 BatchPauseMembers delete refresh token error: %v", err)
				}
				if err := iter.Err(); err != nil {
					logger.Errorf(" 4 BatchPauseMembers delete refresh token error: %v", err)
				}
			}()
		}
	}

	common.ResponseResult(c, nil)
}

// 批量删除用户
func (h *Handler) BatchDeleteMembers(c *gin.Context) {
	var req dto.BatchDeleteMembersRequest
	req.ProductID = c.GetString("product_id")
	req.DeptCode = c.Param("dept_code")
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("BatchDeleteMembers ShouldBindJSON error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 批量删除用户
	batchDeleteUser := &user.BatchDeleteUserRequest{
		ProductID: req.ProductID,
		UserCodes: req.UserCodes,
		OrgCode:   req.OrgCode,
	}
	if err := model.UserRepo.BatchDeleteUser(c.Request.Context(), batchDeleteUser); err != nil {
		logger.Errorf("BatchDeleteMembers BatchDeleteUser error: %v", err)
		common.ResponseResult(c, errors.DeleteError)
		return
	}

	// 删除部门成员关系
	filter := bson.M{
		"product_id": req.ProductID,
		"org_code":   req.OrgCode,
		"user_code":  bson.M{"$in": req.UserCodes},
	}
	if err := model.DepartmentUserRelationRepo.BatchDeleteDepartmentUserRelation(c.Request.Context(), filter); err != nil {
		logger.Errorf("BatchDeleteMembers BatchDeleteDepartmentUserRelation error: %v", err)
		common.ResponseResult(c, errors.DeleteError)
		return
	}

	// 删除所属用户组关系
	filter = bson.M{
		"product_id": req.ProductID,
		"user_code":  bson.M{"$in": req.UserCodes},
	}
	if err := model.UserGroupRelationRepo.BatchDeleteRelation(c.Request.Context(), filter); err != nil {
		logger.Errorf("BatchDeleteMembers BatchDeleteRelation error: %v", err)
		common.ResponseResult(c, errors.DeleteError)
		return
	}
	common.ResponseResult(c, nil)
}

// SearchMembers 搜索用户
func (h *Handler) SearchMembers(c *gin.Context) {
	var req dto.SearchDepartmentMemberRequest
	req.ProductID = c.GetString("product_id")
	req.DeptCode = c.Param("dept_code")

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("SearchMembers ShouldBindJSON error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	searchParams := &user.SearchUserRequest{
		ProductID:    req.ProductID,
		OrgCode:      req.OrgCode,
		DeptCode:     req.DeptCode,
		UserName:     req.UserName,
		Mobile:       req.Mobile,
		Email:        req.Email,
		DeptRole:     req.DeptRoleCode,
		Status:       req.Status,
		ActiveStatus: req.ActiveStatus,
		Page:         req.Page,
		Limit:        req.Limit,
	}

	members, total, err := model.UserRepo.SearchUser(c.Request.Context(), searchParams)
	if err != nil {
		logger.Errorf("SearchMembers error: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}

	orgConfig, err := model.OrgPlatformConfigRepo.FindOne(c,
		bson.M{"product_id": req.ProductID, "range_type": 0, "org_code": req.OrgCode}, options.FindOne())
	if err != nil {
		logger.Errorf("SearchMembers error: %v", err)

	}
	displayNames := make([]string, 0)
	if orgConfig != nil {
		displayNames = orgConfig.SourceConfig.GetDisplayName()
	}

	response := dto.SearchDepartmentMemberResponse{
		Total: total,
		Items: make([]dto.DepartmentMemberResponse, len(members)),
	}

	for i, member := range members {
		// 获取用户组关系
		groupCodes, err := model.UserGroupRelationRepo.GetUserGroups(c.Request.Context(), member.ProductID, member.UserCode)
		if err != nil {
			logger.Errorf("SearchMembers GetUserGroups error: %v", err)
			common.ResponseResult(c, errors.SelectError)
			return
		}
		member = convertUsername(c.Request.Context(), member, displayNames)
		response.Items[i] = convertToResponse(req.ProductID, member, groupCodes, req.DeptCode)
	}

	common.ResponseResult(c, response)
}

// GetMemberDetail 获取用户详情
func (h *Handler) GetMemberDetail(c *gin.Context) {
	var req dto.GetMemberDetailRequest
	req.ProductID = c.GetString("product_id")
	req.DeptCode = c.Param("dept_code")
	req.UserCode = c.Param("user_code")
	if err := req.Validate(); err != nil {
		logger.Errorf("GetMemberDetail Validate error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	member, err := model.UserRepo.GetUserDetail(c.Request.Context(), &user.GetUserDetailRequest{
		ProductID: req.ProductID,
		// OrgCode:   req.OrgCode,
		UserCode: req.UserCode,
	})
	if err != nil {
		logger.Errorf("GetMemberDetail error: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}

	if member == nil {
		logger.Errorf("GetMemberDetail error: member not found")
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	// 3. 获取用户组关系信息
	groupCodes, err := model.UserGroupRelationRepo.GetUserGroups(c.Request.Context(), req.ProductID, req.UserCode)
	if err != nil {
		logger.Errorf("GetMemberDetail GetUserGroups error: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}

	common.ResponseResult(c, convertToResponse(req.ProductID, member, groupCodes, req.DeptCode))
}

// convertToResponse 将模型转换为响应DTO
func convertToResponse(productId string, user *user.OrganizationUser, groupCodes []string, deptCode string) dto.DepartmentMemberResponse {

	departmentMap, err := model.DepartmentRepo.GetDepartmentMap(context.Background(), productId)
	if err != nil {
		logger.Errorf("convertToResponse GetDepartmentMap error: %v", err)
		return dto.DepartmentMemberResponse{}
	}
	orgPlatfromNameMap, err := model.OrgPlatformConfigRepo.GetOrgPlatfromNameMap(context.Background(), productId)
	if err != nil {
		logger.Errorf("convertToResponse GetOrgPlatfromNameMap error: %v", err)
		return dto.DepartmentMemberResponse{}
	}

	deptPathInfos := make([][]department.DepartmentMiniInfo, 0)
	if len(user.DeptPath) > 0 {
		for _, firstItems := range user.DeptPath {
			deptPathInfo := make([]department.DepartmentMiniInfo, 0)
			for _, deptCode := range firstItems {
				if department, ok := departmentMap[deptCode]; ok {
					deptPathInfo = append(deptPathInfo, department)
				}
			}
			deptPathInfos = append(deptPathInfos, deptPathInfo)
		}
	}
	orgName := orgPlatfromNameMap[user.OrgCode]

	response := dto.DepartmentMemberResponse{
		UserCode:              user.UserCode,
		UserName:              user.UserName,
		Account:               user.Account,
		OrgCode:               user.OrgCode,
		OrgName:               orgName,
		DeptCode:              deptCode,
		DeptPath:              user.DeptPath,
		Mobile:                user.Mobile,
		Email:                 user.Email,
		Position:              user.Position,
		ValidTime:             user.ValidTime,
		IsActivationEmailSent: user.IsActivationEmailSent,
		GroupCodes:            groupCodes,
		Status:                user.Status,
		BindDeviceStatus:      user.BindDeviceStatus,
		DeptPathInfos:         deptPathInfos,
		IsActive:              user.IsActive,
		DeptRoles:             user.DeptRoles,
		UserRoles:             user.UserRoles,
	}
	if response.DeptRoles == nil {
		response.DeptRoles = make([]string, 0)
	}
	if response.UserRoles == nil {
		response.UserRoles = make([]string, 0)
	}

	return response
}

// generateUserCode 生成用户编码
func generateUserCode() string {
	return "USER" + strings.ReplaceAll(uuid.New().String(), "-", "")
}

// convertUsername 使用反射和结构体标签，根据 displayNames 动态构建用户名。
// displayNames 包含了一系列字段的 json 标签名，函数会按照这个顺序拼接字段值来生成新的 UserName。
func convertUsername(ctx context.Context, orgUser *user.OrganizationUser, displayNames []string) *user.OrganizationUser {
	// 如果没有指定显示名称或用户对象为空，则直接返回，不做任何更改。
	if len(displayNames) == 0 || orgUser == nil {
		return orgUser
	}

	// 使用反射获取指针指向的结构体实例的值(Value)和类型(Type)。
	v := reflect.ValueOf(orgUser).Elem()
	t := v.Type()

	// 创建一个 map 用于存储 json 标签名到字段值的映射，以提高后续的查找效率。
	tagToValue := make(map[string]string)
	for i := 0; i < v.NumField(); i++ {
		fieldVal := v.Field(i)
		fieldType := t.Field(i)

		// 我们只处理字符串类型的字段。
		if fieldVal.Kind() != reflect.String {
			continue
		}

		// 获取 "json" 标签，并解析出标签名（忽略 ",omitempty" 等选项）。
		jsonTag := fieldType.Tag.Get("bson")
		tagName := strings.Split(jsonTag, ",")[0]

		// 如果标签名有效（不为空或"-"），则存入 map。
		if tagName != "" && tagName != "-" {
			tagToValue[tagName] = fieldVal.String()
		}
	}

	// 根据 displayNames 的顺序，从 map 中查找并构建用户名的各个部分。
	var usernameParts []string
	for _, name := range displayNames {
		// 如果在 map 中找到了对应的值，并且该值不为空，则添加到 parts 中。
		if value, ok := tagToValue[name]; ok && value != "" {
			usernameParts = append(usernameParts, value)
		}
	}

	// 如果成功构建了用户名的组成部分，则用空格拼接并更新 UserName 字段。
	if len(usernameParts) > 0 {
		orgUser.UserName = strings.Join(usernameParts, "|")
	}

	return orgUser
}
