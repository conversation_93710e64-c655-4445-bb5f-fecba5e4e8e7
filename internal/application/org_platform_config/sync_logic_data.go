package org_platform_config

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"sase_cloud_api/internal/model"
	"sase_cloud_api/internal/model/department"
	deparment_member "sase_cloud_api/internal/model/department_member"
	"sase_cloud_api/internal/model/organization_role"
	sync_record "sase_cloud_api/internal/model/sync_record"
	"sase_cloud_api/pkg/logger"
	"sase_cloud_api/pkg/utils"
	"sase_cloud_api/pkg/utils/third_party"
	"strings"
	"sync"
	"time"
)

// SyncService 定义基于 OAService 的同步服务
type SyncService struct {
	OAService third_party.OAService
	cacheData map[string][]string
}

// NewSyncService 创建一个新的 SyncService 实例
func NewSyncService(oaSvc third_party.OAService) *SyncService {
	return &SyncService{
		OAService: oaSvc,
		cacheData: make(map[string][]string),
	}
}

// SyncDepartments 同步部门数据
func (s *SyncService) SyncDepartments(ctx context.Context, departList chan *DepartmentList, language string) {
	logger.Infof("开始同步部门数据 for OrgCode")
	var (
		chanDepart = make(chan *DepartmentList, 20)
		wg         sync.WaitGroup
	)
	for depart := range departList {

		departBaseInfo, err := s.OAService.GetDepartInfo(ctx, &third_party.OaCodeStruct{
			SourceCode: depart.SourceCode,
			ParentCode: depart.ParentCode,
			DN:         depart.LdapDn,
			Language:   language,
		})

		if err != nil {
			logger.Warnf("failed to call get DepartInfoData: %s", err)
			return
		}

		deptCode := cast.ToString(departBaseInfo.SourceCode)

		if cast.ToString(departBaseInfo.SourceCode) == "1" {
			deptCode = depart.OrgCode
		}
		parentDeptCode := cast.ToString(departBaseInfo.SourceParentDeptCode)

		if departBaseInfo.SourceParentDeptCode == "1" || depart.SourceCode == "" || departBaseInfo.SourceParentDeptCode == "" {
			parentDeptCode = cast.ToString(depart.OrgCode)
		}

		if departBaseInfo.LdapDN != nil {
			depart.LdapDn = *departBaseInfo.LdapDN
		}
		if !depart.Ignore {

			if err := model.DepartmentRepo.CreateDepartment(ctx, &department.Department{
				ProductID:      depart.ProductId,
				DeptCode:       deptCode,
				DeptName:       departBaseInfo.DeptName,
				OrgCode:        depart.OrgCode,
				ParentDeptCode: parentDeptCode,
				CreatedAt:      time.Now().Unix(),
				UpdatedAt:      time.Now().Unix(),
				HasSubDept:     depart.HaveSubDept,
				Status:         1,
				ImportBatchNo:  depart.BatchNo,
			}); err != nil {
				logger.Warnf("failed to create department: %v", err)
			}
		}
		wg.Add(1)
		go func() {
			defer wg.Done()
			_ = model.SyncRecord.CreateRecord(ctx, &sync_record.Record{
				Operator:     "",
				OperatorType: depart.OperatorType,
				StartTime:    time.Now().Unix(),
				EndTime:      time.Now().Unix(),
				Status:       0,
				OrgCode:      depart.OrgCode,
				Object:       sync_record.ObjectTypeOfDepartment,
				ObjectCode:   deptCode,
				ObjectName:   depart.Name,
				Event:        "event.department.sync",
				BatchNo:      depart.BatchNo,
			})
		}()

		depart.SourceCode = deptCode

		chanDepart <- depart
		wg.Add(1)
		go func() {
			defer wg.Done()
			s.GetDepartmentUserList(ctx, chanDepart, language)
		}()
	}

}

func (s *SyncService) LoadRootDepartment(ctx context.Context, orgCode, orgName, batchNo, sourceType, productId, language string) {
	var (
		departmentInfoChan = make(chan *DepartmentList, 0)
	)
	s.cacheData[orgCode] = append(s.cacheData[orgCode], orgCode)
	go s.SyncDepartments(ctx, departmentInfoChan, language)
	// 创建根组织
	if s.OAService.GetPlatformType(ctx) == third_party.PlatformTypeDingTalk {
		departmentInfoChan <- &DepartmentList{
			DeptID:       1,
			Name:         orgName,
			OrgCode:      orgCode,
			ParentID:     0,
			ProductId:    productId,
			HaveSubDept:  true,
			BatchNo:      batchNo,
			OperatorType: sync_record.OperatorTypeOfAuto,
			LdapDn:       "",
			SourceCode:   "1",
			ParentCode:   "",
		}
	} else if s.OAService.GetPlatformType(ctx) == third_party.PlatformTypeLDAP {
		// 保证架构一致
		if err := model.DepartmentRepo.CreateDepartment(ctx, &department.Department{
			ProductID:      productId,
			DeptCode:       orgCode,
			DeptName:       orgName,
			OrgCode:        orgCode,
			ParentDeptCode: "0",
			CreatedAt:      time.Now().Unix(),
			UpdatedAt:      time.Now().Unix(),
			HasSubDept:     true,
			Status:         1,
			ImportBatchNo:  batchNo,
		}); err != nil {
			logger.Warnf("failed to create department: %v", err)
		}
	}

	go func() {
		_ = model.SyncRecord.CreateRecord(ctx, &sync_record.Record{
			Operator:     "",
			OperatorType: sync_record.OperatorTypeOfAuto,
			StartTime:    time.Now().Unix(),
			EndTime:      time.Now().Unix(),
			Status:       0,
			OrgCode:      orgCode,
			Object:       sync_record.ObjectTypeOfOrganization,
			ObjectCode:   orgCode,
			ObjectName:   orgName,
			ObjectType:   sourceType,
			Event:        "event.organization.create",
			BatchNo:      batchNo,
		})
	}()

	close(departmentInfoChan)

}

func (s *SyncService) GetDepartmentUserList(ctx context.Context, userList chan *DepartmentList, language string) {

	for depart := range userList {
		employees, err := s.OAService.ListDepartEmployees(ctx, &third_party.OaCodeStruct{
			SourceCode: depart.SourceCode,
			DN:         depart.LdapDn,
			Language:   language,
		})
		if err != nil {
			logger.Warnf("decode employee Data error,err:%s", err)
			return
		}
		departPath := []string{depart.SourceCode}
		if v, ok := s.cacheData[depart.SourceCode]; ok {
			departPath = cast.ToStringSlice(v)
		}
		for _, employee := range employees {

			roles := make([]string, 0, len(employee.Roles))
			var orgRoles = make([]*organization_role.OrganizationRole, 0, len(employee.Roles))
			var orgRolesRecord = make([]*sync_record.Record, 0, len(employee.Roles))

			for _, role := range employee.Roles {

				orgRoles = append(orgRoles, &organization_role.OrganizationRole{
					OrgCode:      depart.OrgCode,
					ProductId:    depart.ProductId,
					RoleCode:     utils.Md5Str(fmt.Sprintf("%s_%d", depart.OrgCode, role.SourceCode)),
					SourceRoleId: cast.ToString(role.SourceCode),
					RoleName:     role.RoleName,
				})

				orgRolesRecord = append(orgRolesRecord, &sync_record.Record{
					OperatorType: depart.OperatorType,
					StartTime:    time.Now().Unix(),
					EndTime:      time.Now().Unix(),
					Status:       0,
					OrgCode:      depart.OrgCode,
					CreateTime:   time.Now().Unix(),
					Object:       sync_record.ObjectTypeOfRole,
					ObjectCode:   utils.Md5Str(fmt.Sprintf("%s_%s", depart.OrgCode, role.SourceCode)),
					ObjectName:   role.RoleName,
					ObjectType:   "",
					Event:        "event.roles.sync",
					BatchNo:      depart.BatchNo,
				})
				_ = model.SyncRecord.BatchCreateRecord(ctx, orgRolesRecord)

				roles = append(roles, utils.Md5Str(fmt.Sprintf("%s_%d", depart.OrgCode, role.SourceCode)))

			}
			member := &deparment_member.DepartmentMember{
				ProductID:             depart.ProductId,
				UserCode:              utils.Md5Str(fmt.Sprintf("%s_%s", depart.OrgCode, employee.SourceCode)),
				UserName:              employee.UserName,
				Account:               employee.Account,
				OrgCode:               depart.OrgCode,
				OriginalDeptCode:      "",
				DeptCode:              depart.SourceCode,
				DeptPath:              [][]string{departPath},
				SourceType:            strings.ToUpper(string(s.OAService.GetPlatformType(ctx))),
				SourceCode:            employee.SourceCode,
				Mobile:                employee.Mobile,
				Email:                 employee.Email,
				Position:              employee.Title,
				Password:              "",
				ValidTime:             0,
				IsActivationEmailSent: false,
				Status:                0,
				BindDeviceStatus:      0,
				CreatedAt:             time.Now().Unix(),
				UpdatedAt:             time.Now().Unix(),
				DeptRole:              "",
				GroupCodes:            nil,
				IsActive:              employee.IsActive,
				BatchNo:               depart.BatchNo,
				Roles:                 roles,
			}

			_ = model.DepartmentMemberRepo.CreateMember(ctx, member)
			_ = model.SyncRecord.CreateRecord(ctx, &sync_record.Record{
				Operator:     "",
				OperatorType: depart.OperatorType,
				StartTime:    time.Now().Unix(),
				EndTime:      time.Now().Unix(),
				Status:       0,
				OrgCode:      depart.OrgCode,
				Object:       sync_record.ObjectTypeOfUser,
				ObjectCode:   member.UserCode,
				ObjectName:   member.UserName,
				Event:        "event.user.sync",
				BatchNo:      member.BatchNo,
			})

		}

	}

}

func (s *SyncService) LoadDepartments(ctx context.Context, obj *third_party.OaCodeStruct, orgCode, batchNo string, departmentInfoChan chan *DepartmentList, productId string) int {
	if obj.SourceCode == "1" || obj.DN == "" {
		s.cacheData[orgCode] = append(s.cacheData[orgCode], orgCode)
	}

	departs, err := s.OAService.ListDeparts(ctx, obj)
	if err != nil {
		logger.Warnf("failed to call ListDeparts error %s", err)
		return 0
	}
	if len(departs) == 0 {
		return 0
	}
	go s.SyncDepartments(ctx, departmentInfoChan, obj.Language)

	if obj.SourceCode == "1" || obj.DN == "" {
		if s.OAService.GetPlatformType(ctx) == third_party.PlatformTypeDingTalk {
			departmentInfoChan <- &DepartmentList{
				DeptID:       1,
				OrgCode:      orgCode,
				ParentID:     0,
				ProductId:    productId,
				HaveSubDept:  true,
				BatchNo:      batchNo,
				OperatorType: sync_record.OperatorTypeOfAuto,
				SourceCode:   "1",
				Ignore:       true,
			}
		}
	}

	for _, depart := range departs {
		ldapDN := ""
		if depart.LdapDN != nil {
			ldapDN = *depart.LdapDN
		}
		var baseDepart = &DepartmentList{
			AutoAddUser:     false,
			CreateDeptGroup: false,
			Name:            depart.DeptName,
			OrgCode:         orgCode,
			ProductId:       productId,
			HaveSubDept:     false,
			BatchNo:         batchNo,
			OperatorType:    sync_record.OperatorTypeOfManual,
			Ignore:          false,
			LdapDn:          ldapDN,
			SourceCode:      depart.SourceCode,
			ParentCode:      obj.ParentCode,
		}

		var parentDeptCode = cast.ToString(depart.SourceParentDeptCode)
		if parentDeptCode == "1" || parentDeptCode == "" {
			parentDeptCode = orgCode
		}

		if v, ok := s.cacheData[parentDeptCode]; ok {
			setData := utils.NewSet[string](v...)
			setData.Add(depart.SourceCode)
			s.cacheData[depart.SourceCode] = append(s.cacheData[depart.SourceCode], setData.ToSlice()...)
		} else {
			s.cacheData[depart.SourceCode] = append(s.cacheData[depart.SourceCode], depart.SourceCode)
		}
		departmentInfoChan <- baseDepart
		time.Sleep(time.Second * 1)

		if total := s.LoadDepartments(ctx, &third_party.OaCodeStruct{SourceCode: depart.SourceCode, DN: ldapDN, Language: obj.Language, ParentCode: depart.SourceCode}, orgCode, batchNo, departmentInfoChan, productId); total > 0 {
			baseDepart.HaveSubDept = true
		}

	}
	return len(departs)
}
