package org_platform_config

import (
	"context"
	"fmt"
	"strings"
	"time"

	"sase_cloud_api/internal/common"
	"sase_cloud_api/internal/config"
	"sase_cloud_api/internal/model"
	"sase_cloud_api/internal/model/department"
	"sase_cloud_api/internal/model/department_user_relation"
	"sase_cloud_api/internal/model/sync_record"
	"sase_cloud_api/internal/util"
	seq "sase_cloud_api/internal/util"
	"sase_cloud_api/pkg/utils"
	"sase_cloud_api/pkg/utils/third_party"

	"sase_cloud_api/internal/common/consts"
	"sase_cloud_api/internal/common/errors"
	customErrors "sase_cloud_api/internal/common/errors"
	"sase_cloud_api/internal/dto"
	"sase_cloud_api/internal/model/org_platform_config"
	"sase_cloud_api/internal/model/user"
	"sase_cloud_api/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
)

// Handler 组织平台配置处理器
type Handler struct {
}

// NewHandler 创建组织平台配置处理器
func NewHandler() *Handler {
	return &Handler{}
}

// CreateOrgPlatformConfig 创建组织平台配置
func (h *Handler) CreateOrgPlatformConfig(c *gin.Context) {
	logger.Debug("接收创建组织平台配置请求")

	var (
		req       dto.CreateOrgPlatformConfigRequest
		oaService third_party.OAService
		err       error
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("请求参数绑定失败: %v", err)
		common.ResponseResult(c, customErrors.InvalidParameter)
		return
	}
	// 当使用第三方平台时，需要验证appkey和secret是否正确
	if req.RangeType == 0 {
		if req.SourceType != org_platform_config.LDAP {
			if count := model.OrgPlatformConfigRepo.Count(c, bson.M{"product_id": c.GetString("product_id"), "source_type": req.SourceType, "app_key": req.AppKey, "secret": req.Secret}); count > 0 {
				common.ResponseResult(c, customErrors.OrgPlatformConfigErrorAPPKeySecretExistsError)
				return
			}
		}
		oaService, err = third_party.NewOA(third_party.NewOAOptionsBuilder().
			SetBaseURL(config.Get().ThirdParty.Address).
			SetPlatformType(org_platform_config.SourceMapping[req.SourceType]).
			SetLdapUrl(req.LdapConfig.LdapURL).
			SetLdapPassword(req.LdapConfig.LdapPassword).
			SetLdapUsername(req.LdapConfig.LdapUsername).
			SetLdapEmployeeFieldMap(req.SourceConfig.GetFiledMapping()).
			SetAppID(req.AppKey).SetAppSecret(req.Secret).
			SetLdapBaseDn(req.LdapConfig.LdapDN))

		if err != nil || !oaService.IsValid(c) {
			common.ResponseResult(c, customErrors.OrgPlatformConfigConnectError)
			return
		}

	}

	// 生成组织代码
	orgCode, err := seq.Sequence.GenerateOrgCode()
	if err != nil {
		logger.Errorf("生成组织代码失败: %v", err)
		common.ResponseResult(c, customErrors.CreateOrgPlatformConfigError)
		return
	}
	// 生成批次号
	batchNo, _ := seq.Sequence.GenerateBatchNumber()

	// 创建组织平台配置
	config := &org_platform_config.OrgPlatformConfig{
		OrgCode:           orgCode,
		OrgName:           req.OrgName,
		SourceType:        req.SourceType,
		SourceConfig:      req.SourceConfig,
		ProductID:         c.GetString("product_id"),
		SyncStatus:        org_platform_config.NOT_SYNCED,
		LastUpdateBatchNo: batchNo,
		Status:            1,
		RangeType:         req.RangeType,
		AppKey:            req.AppKey,
		Secret:            req.Secret,
		CorpId:            req.CorpId,
		AutoSync:          req.AutoSync,
		AutoSyncTime:      req.AutoSyncTime,
		SyncMode:          req.SyncMode,
		UpdateBy:          c.GetString("user_name"),
		CreateBy:          c.GetString("user_name"),
		ThirdAuthStatus:   req.ThirdAuthStatus,
		LdapConfig: &org_platform_config.LdapConfig{
			LdapURL:      req.LdapConfig.LdapURL,
			LdapUsername: req.LdapConfig.LdapUsername,
			LdapPassword: req.LdapConfig.LdapPassword,
			LdapDN:       req.LdapConfig.LdapDN,
		},
	}

	if err := model.OrgPlatformConfigRepo.Create(c, config); err != nil {
		logger.Errorf("创建组织平台配置失败: %v", err)
		common.ResponseResult(c, customErrors.CreateOrgPlatformConfigError)
		return
	}

	logger.Infof("成功创建组织平台配置: %s", orgCode)
	// 创建组织的同时 会自动创建一个根depart
	if req.RangeType == 0 {
		go func() {
			NewSyncService(oaService).LoadRootDepartment(c, orgCode, req.OrgName, batchNo, string(req.SourceType), c.GetString("product_id"), "zh_CN")
		}()
	} else if req.RangeType == 1 {
		go func() {
			_ = model.DepartmentRepo.CreateDepartment(c, &department.Department{
				ProductID:      config.ProductID,
				DeptCode:       orgCode,
				DeptName:       req.OrgName,
				OrgCode:        orgCode,
				ParentDeptCode: "0",
				HasSubDept:     true,
				ImportBatchNo:  batchNo,
				SourceOrgCode:  "",
			})

			_ = model.OrgPlatformConfigRepo.UpdateImpl(c, bson.M{"org_code": orgCode, "product_id": c.GetString("product_id")}, bson.M{"$set": bson.M{"sync_status": 2}})
		}()
	}

	common.ResponseResult(c, "success", dto.ToOrgPlatformConfigResponse(config))
}

// UpdateOrgPlatformConfig 更新组织平台配置
func (h *Handler) UpdateOrgPlatformConfig(c *gin.Context) {
	logger.Debug("接收更新组织平台配置请求")

	orgCode := c.Param("org_code")
	if orgCode == "" {
		logger.Error("组织代码为空")
		common.ResponseResult(c, customErrors.InvalidParameter)
		return
	}

	var req dto.UpdateOrgPlatformConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("请求参数绑定失败: %v", err)
		common.ResponseResult(c, customErrors.InvalidParameter)
		return
	}

	// 查找现有配置
	existingConfig, err := model.OrgPlatformConfigRepo.FindByCode(c, orgCode, c.GetString("product_id"))
	if err != nil {
		logger.Errorf("查找组织平台配置失败: %v", err)
		common.ResponseResult(c, customErrors.OrgPlatformConfigNotFound)
		return
	}

	// 生成新的批次号
	//batchNo := time.Now().Format("20060102") + uuid.New().String()[:3]

	// 更新配置
	existingConfig.OrgName = req.OrgName
	existingConfig.SourceConfig = req.SourceConfig

	existingConfig.CorpId = req.CorpId
	existingConfig.AutoSyncTime = req.AutoSyncTime
	existingConfig.AutoSync = req.AutoSync
	existingConfig.UpdateBy = c.GetString("user_name")
	existingConfig.ThirdAuthStatus = req.ThirdAuthStatus
	existingConfig.LdapConfig = &org_platform_config.LdapConfig{
		LdapURL:      req.LdapConfig.LdapURL,
		LdapUsername: req.LdapConfig.LdapUsername,
		LdapPassword: req.LdapConfig.LdapPassword,
		LdapDN:       req.LdapConfig.LdapDN,
	}
	if req.Secret != "" {
		existingConfig.Secret = req.Secret
	}
	if req.AppKey != "" {
		existingConfig.AppKey = req.AppKey
	}
	existingConfig.SyncMode = req.SyncMode
	// 验证app_key和secret
	if existingConfig.RangeType == 0 {

		oaService, err := third_party.NewOA(third_party.NewOAOptionsBuilder().
			SetBaseURL(config.Get().ThirdParty.Address).
			SetPlatformType(org_platform_config.SourceMapping[existingConfig.SourceType]).
			SetLdapUrl(existingConfig.GetLdapURL()).
			SetLdapPassword(existingConfig.GetLdapPassword()).
			SetLdapUsername(existingConfig.GetLdapUsername()).
			SetLdapEmployeeFieldMap(existingConfig.GetFiledMapping()).
			SetAppID(existingConfig.AppKey).SetAppSecret(existingConfig.Secret).
			SetLdapBaseDn(existingConfig.GetLdapDN()))
		if err != nil || !oaService.IsValid(c) {
			common.ResponseResult(c, customErrors.OrgPlatformConfigConnectError)
			return
		}

	}

	if err := model.OrgPlatformConfigRepo.Update(c, existingConfig); err != nil {
		logger.Errorf("更新组织平台配置失败: %v", err)
		common.ResponseResult(c, customErrors.OrgPlatformConfigUpdateError)
		return
	}

	// 更新根部门名称
	model.DepartmentRepo.UpdateDepartment(c, c.GetString("product_id"), orgCode, &department.Department{
		DeptName: req.OrgName,
	})

	logger.Infof("成功更新组织平台配置: %s", orgCode)
	common.ResponseResult(c, "success", dto.ToOrgPlatformConfigResponse(existingConfig))
}

// DeleteOrgPlatformConfig 删除组织平台配置
func (h *Handler) DeleteOrgPlatformConfig(c *gin.Context) {
	logger.Debug("接收删除组织平台配置请求")

	orgCode := c.Param("org_code")
	if orgCode == "" {
		logger.Error("组织代码为空")
		common.ResponseResult(c, customErrors.InvalidParameter)
		return
	}

	if err := model.OrgPlatformConfigRepo.Delete(c, orgCode, c.GetString("user_name"), c.GetString("product_id")); err != nil {
		logger.Errorf("删除组织平台配置失败: %v", err)
		common.ResponseResult(c, customErrors.OrgPlatformConfigDeleteError)
		return
	}

	logger.Infof("成功删除组织平台配置: %s", orgCode)
	common.ResponseResult(c, "success", nil)
}

// SearchOrgPlatformConfigs 搜索组织平台配置
func (h *Handler) SearchOrgPlatformConfigs(c *gin.Context) {
	logger.Debug("接收搜索组织平台配置请求")

	var req dto.SearchOrgPlatformConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("请求参数绑定失败: %v", err)
		common.ResponseResult(c, customErrors.NewBadRequestError("参数无效", err.Error()))
		return
	}

	// 转换查询参数
	params := &org_platform_config.SearchParams{
		OrgName:    req.OrgName,
		SyncStatus: req.SyncStatus,
		RangeType:  req.RangeType,
		Page:       req.Page,
		Limit:      req.Limit,
		ProductID:  c.GetString("product_id"),
	}

	configs, total, err := model.OrgPlatformConfigRepo.Search(c, params)
	if err != nil {
		logger.Errorf("搜索组织平台配置失败: %v", err)
		common.ResponseResult(c, customErrors.NewInternalServerError("搜索组织平台配置失败", err.Error()))
		return
	}

	logger.Infof("成功搜索组织平台配置，共找到 %d 条记录", total)
	common.ResponseResult(c, "success", dto.ToOrgPlatformConfigListResponse(configs, total))
}

// GetOrgPlatformConfigDetail 获取组织平台配置详情
func (h *Handler) GetOrgPlatformConfigDetail(c *gin.Context) {
	logger.Debug("接收获取组织平台配置详情请求")

	orgCode := c.Param("org_code")
	if orgCode == "" {
		logger.Error("组织代码为空")
		common.ResponseResult(c, customErrors.InvalidParameter)
		return
	}

	config, err := model.OrgPlatformConfigRepo.FindByCode(c, orgCode, c.GetString("product_id"))
	if err != nil {
		logger.Errorf("获取组织平台配置详情失败: %v", err)
		common.ResponseResult(c, customErrors.OrgPlatformConfigNotFound)
		return
	}

	logger.Infof("成功获取组织平台配置详情: %s", orgCode)
	common.ResponseResult(c, "success", dto.ToOrgPlatformConfigResponse(config))
}

func (h *Handler) SyncOrgPlatformData(c *gin.Context) {
	orgCode := c.Param("org_code")
	if orgCode == "" {
		logger.Error("组织代码为空")
		common.ResponseResult(c, customErrors.InvalidParameter)
		return
	}

	conf, err := model.OrgPlatformConfigRepo.FindByCode(c, orgCode, c.GetString("product_id"))
	if err != nil {
		logger.Errorf("获取组织平台配置详情失败: %v", err)
		common.ResponseResult(c, customErrors.OrgPlatformConfigNotFound)
		return
	}

	oaService, err := third_party.NewOA(third_party.NewOAOptionsBuilder().
		SetBaseURL(config.Get().ThirdParty.Address).
		SetPlatformType(org_platform_config.SourceMapping[conf.SourceType]).
		SetLdapUrl(conf.GetLdapURL()).
		SetLdapPassword(conf.GetLdapPassword()).
		SetLdapUsername(conf.GetLdapUsername()).
		SetLdapEmployeeFieldMap(conf.GetFiledMapping()).
		SetAppID(conf.AppKey).SetAppSecret(conf.Secret).
		SetLdapBaseDn(conf.GetLdapDN()))
	if err != nil || !oaService.IsValid(c) {
		common.ResponseResult(c, customErrors.OrgPlatformConfigConnectError)
		return
	}
	client := NewSyncService(oaService)
	var chanData = make(chan *DepartmentList, 10)
	batchNo, _ := seq.Sequence.GenerateBatchNumber()

	go func() {
		_ = model.OrgPlatformConfigRepo.UpdateImpl(c, bson.M{"org_code": orgCode, "status": 1}, bson.M{"$set": bson.M{
			"sync_status":    org_platform_config.SYNCING,
			"last_sync_time": time.Now().Unix(),
			"update_at":      time.Now().Unix(),
		}})

	}()
	//删除orgCode 关联的departments和departments_members和角色,并将organization_user的dept_path 清空
	_ = model.DepartmentRepo.DeleteManyDepartment(c, bson.M{"org_code": orgCode, "product_id": c.GetString("product_id"), "parent_dept_code": bson.M{"$ne": "0"}})
	_ = model.DepartmentMemberRepo.BatchDeleteUsersRelation(c, bson.M{"org_code": orgCode, "product_id": c.GetString("product_id")})
	_ = model.DepartmentMemberRepo.UnsetOrganizationUser(c, bson.M{"org_code": orgCode, "product_id": c.GetString("product_id")})
	_ = model.OrganizationRoleRepo.BatchDeleteRoles(c, bson.M{"org_code": orgCode, "product_id": c.GetString("product_id")})

	client.LoadDepartments(context.Background(), &third_party.OaCodeStruct{
		SourceCode: "1",
		DN:         "",
		Language:   "zh_CN",
		ParentCode: "",
	}, conf.OrgCode, batchNo, chanData, c.GetString("product_id"))
	close(chanData)

	go func() {
		_ = model.OrgPlatformConfigRepo.UpdateImpl(c, bson.M{"org_code": orgCode, "status": 1}, bson.M{"$set": bson.M{
			"sync_status":          org_platform_config.SYNCED,
			"last_update_batch_no": batchNo,
			"last_sync_time":       time.Now().Unix(),
			"update_at":            time.Now().Unix(),
		}})

		_ = model.SyncRecord.CreateRecord(c, &sync_record.Record{
			Operator:     "",
			OperatorType: sync_record.OperatorTypeOfManual,
			StartTime:    time.Now().Unix(),
			EndTime:      time.Now().Unix(),
			Status:       0,
			OrgCode:      orgCode,
			Object:       sync_record.ObjectTypeOfOrganization,
			ObjectCode:   orgCode,
			ObjectName:   conf.OrgName,
			ObjectType:   string(conf.SourceType),
			Event:        "event.organization.sync",
			BatchNo:      batchNo,
		})
	}()

	common.ResponseResult(c, "success")

}
func (h *Handler) CountOrgPlatformData(c *gin.Context) {

	organizationCount := model.OrgPlatformConfigRepo.Count(c, bson.M{"product_id": c.GetString("product_id"), "status": 1})

	usersCount := model.DepartmentMemberRepo.CountMemberData(c, bson.M{"product_id": c.GetString("product_id")})

	common.ResponseResult(c, "success", map[string]interface{}{
		"organization_count": organizationCount,
		"users_count":        usersCount,
		"sync_count":         model.SyncRecord.CountValue(c, bson.M{"object": sync_record.ObjectTypeOfOrganization, "status": bson.M{"$ne": 0}}),
	})
}

// CustomOrgImport 自定义组织导入部门
func (h *Handler) CustomOrgImport(c *gin.Context) {
	// 获取产品ID
	productID := c.GetString("product_id")
	if productID == "" {
		logger.Error("CustomImportDepartments error: product_id is empty")
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 解析参数
	isSendEmail := c.PostForm("is_send_email")
	if isSendEmail == "" || (isSendEmail != "1" && isSendEmail != "0") {
		logger.Error("CustomImportDepartments error: is_send_email is empty")
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	orgCode := c.Param("org_code")
	if orgCode == "" {
		logger.Error("CustomImportDepartments error: org_code is empty")
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	org, err := model.OrgPlatformConfigRepo.FindByCode(context.Background(), orgCode, productID)
	if err != nil {
		logger.Errorf("CustomImportDepartments error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}
	orgName := org.OrgName

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		logger.Errorf("CustomImportDepartments error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 打开上传的文件
	fileHandle, err := file.Open()
	if err != nil {
		logger.Errorf("CustomImportDepartments open file error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}
	defer fileHandle.Close()

	// 解析Excel文件
	excel, err := excelize.OpenReader(fileHandle)
	if err != nil {
		logger.Errorf("CustomImportDepartments parse excel error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}
	defer excel.Close()

	// 获取第一个工作表
	sheetName := excel.GetSheetName(0)
	if sheetName == "" {
		logger.Error("CustomImportDepartments error: sheet not found")
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 读取Excel数据，从第三行开始（跳过前两行标题）
	rows, err := excel.GetRows(sheetName)
	if err != nil {
		logger.Errorf("CustomImportDepartments get rows error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 检查数据行数是否足够
	if len(rows) < 3 {
		logger.Error("CustomImportDepartments error: not enough data rows")
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 定义部门数据结构
	type DepartmentImport struct {
		Name       string // 姓名
		Account    string // 账号
		Mobile     string // 手机号
		Email      string // 邮箱
		Position   string // 职位
		Password   string // 登录密码
		Level1     string // 一级部门
		Level2     string // 二级部门
		Level3     string // 三级部门
		Level4     string // 四级部门
		ExpireTime string // 过期时间
	}

	// 获取部署邀请信息
	deployInviteInfo, err := model.DeployInviteInfoRepo.FindOne(c.Request.Context(), bson.M{"product_id": productID, "expired_time": bson.M{"$gt": time.Now().Unix()}})

	if err != nil {
		logger.Errorf("GetDeployInfo error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	link := ""
	if deployInviteInfo != nil && deployInviteInfo.Code != "" {
		link = fmt.Sprintf(config.Get().Server.DeployInviteUrl, deployInviteInfo.Code)
	}

	// 从第三行开始读取数据
	go func(rows [][]string) {
		deptFullPathNameMap := make(map[string][]string)      // 部门全路径名称 =》 部门全路径ID列表
		accounts := make([]string, 0)                         // 收集所有账号
		accountToDeptMap := make(map[string]DepartmentImport) // 账号到部门信息的映射

		// 第一遍遍历：收集所有账号和相关信息
		for i := 2; i < len(rows); i++ {
			row := rows[i]
			if len(row) < 10 {
				row = append(row, make([]string, 10-len(row))...)
				logger.Warnf("CustomImportDepartments error: row %d data incomplete", i+1)
			}

			dept := DepartmentImport{
				Name:     strings.TrimSpace(row[0]),
				Account:  strings.TrimSpace(row[1]),
				Mobile:   strings.TrimSpace(row[2]),
				Email:    strings.TrimSpace(row[3]),
				Position: strings.TrimSpace(row[4]),
				Password: strings.TrimSpace(row[5]),
				Level1:   strings.TrimSpace(row[6]),
				Level2:   strings.TrimSpace(row[7]),
				Level3:   strings.TrimSpace(row[8]),
				Level4:   strings.TrimSpace(row[9]),
			}

			if dept.Name == "" || dept.Account == "" || dept.Level1 == "" {
				logger.Errorf("CustomImportDepartments error: row %d missing required fields", i+1)
				continue
			}

			accounts = append(accounts, dept.Account)
			accountToDeptMap[dept.Account] = dept
		}

		// 根据账号进行去重
		accounts = utils.RemoveDuplicate(accounts)

		// 分批次对账号进行查询
		batchSize := 1000
		for i := 0; i < len(accounts); i += batchSize {
			ctx := context.Background()
			batchAccounts := accounts[i:min(i+batchSize, len(accounts))]

			// 批量查询已存在的用户
			existingUsers, err := model.UserRepo.FindMany(context.Background(), bson.M{"account": bson.M{"$in": batchAccounts}, "product_id": productID}, nil)
			if err != nil {
				logger.Errorf("CustomImportDepartments error: %v", err)
				return
			}

			// 创建已存在用户的映射
			existingUserMap := make(map[string]string)
			for _, user := range existingUsers {
				existingUserMap[user.Account] = user.UserCode
			}

			// 准备批量创建的用户列表
			var usersToCreate []*user.CreateUserRequest
			var usersToUpdate []*user.UpdateUserRequest
			accountToUserCode := make(map[string]string)

			// 准备发送邮件的数据
			var SendEmail []*user.SendEmailStruct

			// 准备批量删除和创建的关系
			var userCodesToDelete []string
			var relationsToCreate []*department_user_relation.DepartmentUserRelation

			// 第二遍遍历：处理用户创建和部门关系
			for _, dept := range accountToDeptMap {
				departmentNames := []string{orgName, dept.Level1, dept.Level2, dept.Level3, dept.Level4}
				depIds, ok := deptFullPathNameMap[strings.Join(departmentNames, "-")]
				if !ok {
					depIds, err = model.DepartmentRepo.GetFullPathByPathNames(ctx, productID, orgCode, departmentNames)
					if err != nil {
						logger.Errorf("CustomImportDepartments error: %v", err)
						continue
					}
					deptFullPathNameMap[strings.Join(departmentNames, "-")] = depIds
				}

				// 将Excel日期转换为时间戳
				expireTime := utils.ExcelDateToTimestamp(dept.ExpireTime)

				userCode, exists := existingUserMap[dept.Account]
				if !exists {
					userCode, err = util.Sequence.GenerateUserCode()
					if err != nil {
						logger.Errorf("生成用户编码失败: %v", err)
						continue
					}
					accountToUserCode[dept.Account] = userCode
					createUserReq := &user.CreateUserRequest{
						UserCode:  userCode,
						ProductID: productID,
						OrgCode:   orgCode,
						UserName:  dept.Name,
						Mobile:    dept.Mobile,
						Email:     dept.Email,
						Account:   dept.Account,
						// Password:              dept.Password,
						Position:              dept.Position,
						Status:                0,
						BindDeviceStatus:      0,
						IsActivationEmailSent: isSendEmail == "1",
						SourceType:            "CUSTOM",
						DeptPath:              [][]string{depIds},
						ValidTime:             expireTime,
					}
					if dept.Password != "" {
						createUserReq.Password, err = util.HashPassword(dept.Password)
						if err != nil {
							logger.Errorf("CustomImportDepartments HashPassword error: %v", err)
							continue
						}
					}
					usersToCreate = append(usersToCreate, createUserReq)

					if isSendEmail == "1" && dept.Email != "" {
						// 发送邮件
						SendEmail = append(SendEmail, &user.SendEmailStruct{
							Link:       link,
							UserName:   dept.Name,
							Account:    dept.Account,
							Password:   dept.Password,
							Email:      dept.Email,
							SourceType: "CUSTOM",
						})
					}
				} else {
					// 更新用户信息
					updateUserReq := &user.UpdateUserRequest{
						UserCode:  userCode,
						ProductID: productID,
						Account:   dept.Account,
						UserName:  dept.Name,
						Mobile:    dept.Mobile,
						Email:     dept.Email,
						Position:  dept.Position,
						// Password:              dept.Password,
						IsActivationEmailSent: isSendEmail == "1",
						DeptPath:              [][]string{depIds},
						ValidTime:             expireTime,
					}
					if dept.Password != "" {
						updateUserReq.Password, err = util.HashPassword(dept.Password)
						if err != nil {
							logger.Errorf("CustomImportDepartments HashPassword error: %v", err)
							continue
						}
					}
					usersToUpdate = append(usersToUpdate, updateUserReq)

					// 发送邮件
					if isSendEmail == "1" && dept.Email != "" {
						SendEmail = append(SendEmail, &user.SendEmailStruct{
							Link:       link,
							UserName:   dept.Name,
							Account:    dept.Account,
							Password:   dept.Password,
							Email:      dept.Email,
							SourceType: "CUSTOM",
						})
					}
				}

				// 删除所有的用户关系
				userCodesToDelete = append(userCodesToDelete, userCode)

				var deptCode string
				if len(depIds) > 0 {
					deptCode = depIds[len(depIds)-1]
				}

				if deptCode != "" {
					// 创建新的关系
					relationsToCreate = append(relationsToCreate, &department_user_relation.DepartmentUserRelation{
						ProductID: productID,
						OrgCode:   orgCode,
						DeptCode:  deptCode,
						UserCode:  userCode,
						DeptPath:  [][]string{depIds},
						Status:    0,
						CreatedAt: time.Now().Unix(),
						UpdatedAt: time.Now().Unix(),
					})
				}
			}

			// 批量删除部门与用户的关系
			batchDeleteDepartmentUserRelation(ctx, productID, orgCode, userCodesToDelete)

			// 批量创建用户
			batchCreateUser(ctx, usersToCreate)

			// 批量更新用户
			batchUpdateUser(ctx, productID, orgCode, usersToUpdate)

			// 批量创建部门与用户的关系
			batchCreateDepartmentUserRelation(ctx, relationsToCreate)

			if isSendEmail == "1" {
				// 发送激活邮件
				_, _ = model.UserRepo.SendEmail(c, consts.SendEmailBusinessType3, SendEmail)
			}
		}

	}(rows)

	common.ResponseResult(c, errors.Success)
}

func (h *Handler) GetOrgMemberFieldMapping(ctx *gin.Context) {

	data := []user.FieldMapping{{
		Key:               "1",
		SystemField:       "user_name",
		SystemFieldText:   "展示姓名",
		DisplayNameFields: []string{"name"},
		Required:          false,
		Tooltip:           "用户在系统中显示的名称",
		Deleteable:        false,
	},
		{
			Key:             "2",
			SystemField:     "email",
			SystemFieldText: "邮箱",
			ThirdPartyField: "email",
			Required:        true,
			Deleteable:      false,
		},
		{
			Key:             "3",
			SystemField:     "user_name",
			SystemFieldText: "用户名",
			ThirdPartyField: "name",
			Required:        true,
			Deleteable:      false,
		},
		{
			Key:             "4",
			SystemField:     "mobile",
			SystemFieldText: "手机",
			ThirdPartyField: "mobile",
			Required:        true,
			Deleteable:      false,
		},
		{
			Key:             "5",
			SystemField:     "position",
			SystemFieldText: "职位",
			ThirdPartyField: "title",
			Required:        true,
			Deleteable:      false,
		},
		{
			Key:             "6",
			SystemField:     "user_code",
			SystemFieldText: "账户唯一 ID",
			ThirdPartyField: "userid",
			Required:        true,
			Deleteable:      false,
		},
	}

	userCustom := []user.LdapConfig{
		{
			Field:     "user_code",
			FieldText: "用户唯一ID",
		},
		{
			Field:     "email",
			FieldText: "邮箱",
		},
		{
			Field:     "user_name",
			FieldText: "用户名",
		},
		{
			Field:     "mobile",
			FieldText: "手机",
		},
		{
			Field:     "position",
			FieldText: "职位",
		},
	}

	common.ResponseResult(ctx, map[string]interface{}{
		"field_mapping":  data,
		"custom_mapping": userCustom,
	})

}

func batchDeleteDepartmentUserRelation(ctx context.Context, productID string, orgCode string, userCodes []string) {
	if len(userCodes) > 0 {
		deleteFilters := bson.M{
			"product_id": productID,
			"org_code":   orgCode,
			"user_code":  bson.M{"$in": userCodes},
		}

		err := model.DepartmentUserRelationRepo.DeleteMany(ctx, deleteFilters)
		if err != nil {
			logger.Errorf("batch delete relations error: %v", err)
		} else {
			logger.Infof("batch delete %d relations successfully", len(userCodes))
		}
	}
}

func batchUpdateUser(ctx context.Context, productID string, orgCode string, users []*user.UpdateUserRequest) {
	if len(users) > 0 {
		if err := model.UserRepo.BatchUpdateUsers(ctx, productID, orgCode, users); err != nil {
			logger.Errorf("batch update users error: %v", err)
		} else {
			logger.Infof("batch update %d users successfully", len(users))
		}
	}
}

func batchCreateUser(ctx context.Context, users []*user.CreateUserRequest) {
	if len(users) > 0 {
		if err := model.UserRepo.BatchCreateUsers(ctx, users); err != nil {
			logger.Errorf("batch create users error: %v", err)
		} else {
			logger.Infof("batch create %d users successfully", len(users))
		}
	}
}

func batchCreateDepartmentUserRelation(ctx context.Context, relations []*department_user_relation.DepartmentUserRelation) {
	if len(relations) > 0 {
		err := model.DepartmentUserRelationRepo.BatchCreateDepartmentUserRelation(ctx, relations)
		if err != nil {
			logger.Errorf("batch create relations error: %v", err)
		} else {
			logger.Infof("batch create %d relations successfully", len(relations))
		}
	}
}
