package deploy

import (
	"context"
	"encoding/json"
	"fmt"
	"sase_cloud_api/internal/common"
	"sase_cloud_api/internal/common/consts"
	"sase_cloud_api/internal/common/errors"
	"sase_cloud_api/internal/config"
	"sase_cloud_api/internal/dto"
	"sase_cloud_api/internal/model"
	"sase_cloud_api/internal/model/deploy_invite_info"
	"time"

	"sase_cloud_api/pkg/logger"
	"sase_cloud_api/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
)

type Handler struct {
}

func NewHandler() *Handler {
	return &Handler{}
}

// GetDeployInfo 获取部署信息
func (h *Handler) GetDeployInfo(c *gin.Context) {
	productId := c.GetString("product_id")

	deployInviteInfo, err := model.DeployInviteInfoRepo.FindOne(c.Request.Context(), bson.M{"product_id": productId, "expired_time": bson.M{"$gt": time.Now().Unix()}})

	if err != nil {
		logger.Errorf("GetDeployInfo error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	link := ""
	if deployInviteInfo != nil && deployInviteInfo.Code != "" {
		link = fmt.Sprintf(config.Get().Server.DeployInviteUrl, deployInviteInfo.Code)
	}

	var response *dto.DeployInfoResponse

	if deployInviteInfo.Code != "" {
		response = &dto.DeployInfoResponse{
			ProductID:   deployInviteInfo.ProductId,
			ExpiredTime: deployInviteInfo.ExpiredTime,
			ValidDays:   deployInviteInfo.ValidDays,
			InviteLink:  link,
		}
	}

	common.ResponseResult(c, response)
}

func (h *Handler) UpsertDeployInfo(c *gin.Context) {
	productId := c.GetString("product_id")

	var req dto.UpsertDeployInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("UpsertDeployInfo error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	if req.ValidDays <= 0 {
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	code, err := utils.SecureRandomString(10)
	if err != nil {
		logger.Errorf("SecureRandomString error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}
	deployInviteInfo := &deploy_invite_info.DeployInviteInfo{
		ProductId:   productId,
		ValidDays:   req.ValidDays,
		Code:        code,
		ExpiredTime: time.Now().AddDate(0, 0, req.ValidDays).Unix(),
	}

	err = model.DeployInviteInfoRepo.UpsertByProductId(c.Request.Context(), deployInviteInfo)

	if err != nil {
		logger.Errorf("UpsertDeployInfo error: %v", err)
		common.ResponseResult(c, errors.UpdateError)
		return
	}
	link := ""
	if deployInviteInfo.Code != "" {
		link = fmt.Sprintf(config.Get().Server.DeployInviteUrl, deployInviteInfo.Code)
	}

	result := dto.DeployInfoResponse{
		ProductID:   deployInviteInfo.ProductId,
		ExpiredTime: deployInviteInfo.ExpiredTime,
		ValidDays:   deployInviteInfo.ValidDays,
		InviteLink:  link,
	}

	common.ResponseResult(c, result)
}

// GetDownloadPackages 获取部署下载包列表
func (h *Handler) GetDownloadPackages(c *gin.Context) {
	productId := c.GetString("product_id")

	packages, err := model.DeployDownloadPkgRepo.Find(c.Request.Context(), bson.M{"product_id": bson.M{"$in": []string{productId, "default"}}})

	if err != nil {
		logger.Errorf("获取部署下载包列表失败: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}

	// 转换为响应格式
	var response []map[string]interface{}
	for _, pkg := range packages {
		pkgData := map[string]interface{}{
			"product_id":   pkg.ProductId,
			"name":         pkg.Name,
			"arch":         pkg.Arch,
			"created_at":   pkg.CreatedAt,
			"download_url": fmt.Sprintf(config.Get().Server.PkgDownloadUrl, pkg.FileName),
			"file_name":    pkg.FileName,
			"md5":          pkg.MD5,
			"platform":     pkg.Platform,
			"sha1":         pkg.SHA1,
			"sha256":       pkg.SHA256,
			"size":         pkg.Size,
			"updated_at":   pkg.UpdatedAt,
			"version":      pkg.Version,
		}
		response = append(response, pkgData)
	}

	common.ResponseResult(c, response)
}

func (h *Handler) DeployInvite(c *gin.Context) {
	code := c.Param("code")

	deployInviteInfo, err := model.DeployInviteInfoRepo.FindOne(c.Request.Context(), bson.M{"code": code, "expired_time": bson.M{"$gt": time.Now().Unix()}})

	if err != nil {
		logger.Errorf("GetDeployInfo error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	if deployInviteInfo.Code == "" {
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	var openEnterprisePwd bool
	var enterprisePwd string

	enterpriseSetting, err := model.EnterpriseSettingRepo.FindByProductIdAndType(context.Background(), deployInviteInfo.ProductId, consts.JoinCompany)
	if err != nil {
		logger.Errorf("GetEnterpriseSetting error: %v", err)
	} else {
		contents := enterpriseSetting.Contents
		if contents != "" {
			contentsMap := make(map[string]interface{})
			err = json.Unmarshal([]byte(contents), &contentsMap)
			if err != nil {
				logger.Errorf("Unmarshal contents error: %v", err)
			}
			openEnterprisePwd = cast.ToBool(contentsMap["open_enterprise_pwd"])
			enterprisePwd = cast.ToString(contentsMap["pwd"])
		}
	}

	packages, err := model.DeployDownloadPkgRepo.Find(c.Request.Context(), bson.M{"product_id": bson.M{"$in": []string{deployInviteInfo.ProductId, "default"}}})

	if err != nil {
		logger.Errorf("获取部署下载包列表失败: %v", err)
	}

	// 转换为响应格式
	var packagesResponse []map[string]interface{}
	for _, pkg := range packages {
		pkgData := map[string]interface{}{
			"product_id":   pkg.ProductId,
			"name":         pkg.Name,
			"arch":         pkg.Arch,
			"created_at":   pkg.CreatedAt,
			"download_url": fmt.Sprintf(config.Get().Server.PkgDownloadUrl, pkg.FileName),
			"file_name":    pkg.FileName,
			"md5":          pkg.MD5,
			"platform":     pkg.Platform,
			"sha1":         pkg.SHA1,
			"sha256":       pkg.SHA256,
			"size":         pkg.Size,
			"updated_at":   pkg.UpdatedAt,
			"version":      pkg.Version,
		}
		packagesResponse = append(packagesResponse, pkgData)
	}

	info := map[string]interface{}{
		"product_id":          deployInviteInfo.ProductId,
		"created_at":          deployInviteInfo.CreatedAt,
		"open_enterprise_pwd": openEnterprisePwd,
		"enterprise_pwd":      enterprisePwd,
		"packages":            packagesResponse,
	}

	common.ResponseResult(c, info)
}
