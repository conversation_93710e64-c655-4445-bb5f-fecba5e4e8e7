package application

import (
	"github.com/gin-gonic/gin"

	"sase_cloud_api/internal/application/app_admin"
	"sase_cloud_api/internal/application/app_health"
	"sase_cloud_api/internal/application/app_portal_category"
	"sase_cloud_api/internal/application/app_portal_config"
	"sase_cloud_api/internal/application/apply_permission_log"
	"sase_cloud_api/internal/application/callback"
	"sase_cloud_api/internal/application/certs"
	"sase_cloud_api/internal/application/deploy"
	"sase_cloud_api/internal/application/device_group"
	"sase_cloud_api/internal/application/enterprise_setting"
	"sase_cloud_api/internal/application/ip_config"
	"sase_cloud_api/internal/application/notify"
	"sase_cloud_api/internal/application/notify_policy"
	"sase_cloud_api/internal/application/org_user_role"
	"sase_cloud_api/internal/application/policy_network_region"
	"sase_cloud_api/internal/application/policy_process_group"
	"sase_cloud_api/internal/application/scene_template"
	"sase_cloud_api/internal/application/ztna_log"
	"sase_cloud_api/internal/config"

	"sase_cloud_api/internal/application/org_platform_member"
	"sase_cloud_api/internal/application/user"

	"sase_cloud_api/internal/application/app_analysis"
	"sase_cloud_api/internal/application/app_base"
	"sase_cloud_api/internal/application/app_category"
	"sase_cloud_api/internal/application/app_category_tag"
	"sase_cloud_api/internal/application/policy"
	"sase_cloud_api/internal/application/user_group"
	"sase_cloud_api/internal/application/user_group_relation"

	"sase_cloud_api/internal/application/department"
	"sase_cloud_api/internal/application/department_member"
	"sase_cloud_api/internal/application/department_role"
	"sase_cloud_api/internal/application/department_role_relation"
	"sase_cloud_api/internal/application/org_platform_config"

	"sase_cloud_api/internal/common"
	"sase_cloud_api/internal/middleware"
	"sase_cloud_api/pkg/logger"
)

// RegisterRoutes 注册所有路由
func RegisterRoutes(r *gin.Engine) {
	logger.Info("开始注册API路由")

	// 全局中间件
	r.Use(middleware.Cors())
	r.Use(middleware.RequestID())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())
	r.Use(middleware.SetContextVal())

	// API版本v1
	v1 := r.Group(config.Get().Server.Context + "/api/v1")
	{
		// 健康检查
		v1.GET("/health", func(c *gin.Context) {
			logger.Debug("健康检查请求")
			common.ResponseResult(c, "ok", gin.H{
				"status": "ok",
			})
		})
		logger.Debug("已注册健康检查路由: /api/v1/health")

		// 钉钉回调相关路由
		callbackRoutes := v1.Group("/callback")
		{
			logger.Debug("注册钉钉回调相关路由")
			callbackHandler := callback.NewHandler()
			callbackRoutes.GET("/dingtalk", callbackHandler.DingTalkCallback)
			logger.Debug("已注册钉钉回调相关路由: GET /dingtalk")
		}

		// 应用分类相关路由
		appCategoryRoutes := v1.Group("/application/apps/classes")
		{
			logger.Debug("注册应用分类相关路由")
			appCategoryHandler := app_category.NewHandler()
			appCategoryRoutes.GET("", appCategoryHandler.ListAppCategories)
			appCategoryRoutes.POST("", appCategoryHandler.CreateAppCategory)
			appCategoryRoutes.PUT("/:category_code", appCategoryHandler.UpdateAppCategory)
			appCategoryRoutes.DELETE("/:category_code", appCategoryHandler.DeleteAppCategory)
			logger.Debug("已注册应用分类相关路由: GET /, POST /, PUT /:category_code, DELETE /:category_code")
		}

		// 门户应用分类相关路由
		appPortalCategoryRoutes := v1.Group("/application/portal/category")
		{
			logger.Debug("注册门户应用分类相关路由")
			appPortalCategoryHandler := app_portal_category.NewHandler()
			appPortalCategoryRoutes.GET("", appPortalCategoryHandler.ListAppPortalCategories)
			appPortalCategoryRoutes.POST("", appPortalCategoryHandler.CreateAppPortalCategory)
			appPortalCategoryRoutes.PUT("/:category_code", appPortalCategoryHandler.UpdateAppPortalCategory)
			appPortalCategoryRoutes.DELETE("/:category_code", appPortalCategoryHandler.DeleteAppPortalCategory)
			appPortalCategoryRoutes.GET("/:category_code", appPortalCategoryHandler.GetAppPortalCategory)
			appPortalCategoryRoutes.GET("/stats", appPortalCategoryHandler.GetAppPortalCategoryStats)
			logger.Debug("已注册门户应用分类相关路由: GET /, POST /, GET /:category_code, PUT /:category_code, DELETE /:category_code, GET /:category_code/stats/app-count")
		}

		// 门户配置相关路由
		appPortalConfigGroup := v1.Group("/application/portal/config")
		{
			logger.Debug("注册门户配置相关路由")
			appPortalConfigHandler := app_portal_config.NewHandler()
			appPortalConfigGroup.POST("", appPortalConfigHandler.CreateOrUpdateAppPortalConfig) // 创建或更新门户配置
			appPortalConfigGroup.PUT("", appPortalConfigHandler.UpdateAppPortalConfig)          // 更新门户配置
			appPortalConfigGroup.GET("", appPortalConfigHandler.GetAppPortalConfig)             // 获取门户配置
			logger.Debug("已注册门户配置相关路由: POST /, PUT /, GET /")
		}

		// 应用标签相关路由
		appCategoryTagRoutes := v1.Group("/application/apps/tags")
		{
			logger.Debug("注册应用标签相关路由")
			appCategoryTagHandler := app_category_tag.NewHandler()
			appCategoryTagRoutes.GET("", appCategoryTagHandler.ListAppCategoryTags)
			appCategoryTagRoutes.POST("", appCategoryTagHandler.CreateAppCategoryTag)
			appCategoryTagRoutes.PUT("/:tag_code", appCategoryTagHandler.UpdateAppCategoryTag)
			appCategoryTagRoutes.DELETE("/:tag_code", appCategoryTagHandler.DeleteAppCategoryTag)
			logger.Debug("已注册应用标签相关路由: GET /, POST /, PUT /:tag_code, DELETE /:tag_code")
		}

		// 应用基础相关路由
		appBaseRoutes := v1.Group("/application/apps")
		{
			logger.Debug("注册应用基础相关路由")
			appBaseHandler := app_base.NewHandler()
			appBaseRoutes.GET("", appBaseHandler.ListApps)
			appBaseRoutes.GET("/:app_code", appBaseHandler.GetAppDetail)
			appBaseRoutes.POST("", appBaseHandler.CreateApp)
			appBaseRoutes.PUT("/:app_code", appBaseHandler.UpdateApp)
			appBaseRoutes.PUT("/:app_code/tags", appBaseHandler.UpdateAppTags)
			appBaseRoutes.PUT("/:app_code/address", appBaseHandler.UpdateAppAddress)
			appBaseRoutes.PUT("/:app_code/health", appBaseHandler.UpdateHealthCheckConfig)
			appBaseRoutes.DELETE("", appBaseHandler.DeleteApp)
			appBaseRoutes.POST("/import", appBaseHandler.ImportApp)
			appBaseRoutes.GET("/export", appBaseHandler.ExportApp)
			appBaseRoutes.GET("/get_relation_policys_by_app_codes", appBaseHandler.GetRelationPolicysByAppCodes)
			logger.Debug("已注册应用基础相关路由: GET /, GET /:app_code, POST /, PUT /:app_code, DELETE /:app_code")
		}

		// 应用管理员相关路由
		appAdminRoutes := v1.Group("/application/admin")
		{
			logger.Debug("注册应用管理员相关路由")
			appAdminHandler := app_admin.NewHandler()
			appAdminRoutes.GET("", appAdminHandler.ListAppAdmins)
			appAdminRoutes.POST("", appAdminHandler.CreateAppAdmin)
			appAdminRoutes.PUT("/:app_admin_code", appAdminHandler.UpdateAppAdmin)
			appAdminRoutes.DELETE("/:app_admin_code", appAdminHandler.DeleteAppAdmin)
			logger.Debug("已注册应用管理员相关路由: GET /, POST /, PUT /:app_admin_code, DELETE /:app_admin_code")
		}

		appHealthRoutes := v1.Group("/application/health")
		{
			logger.Debug("注册应用健康状态相关路由")
			appHealthHandler := app_health.NewHandler()
			appHealthRoutes.GET("/batch_apps", appHealthHandler.GetBatchAppHealths) // 批量查询应用健康状态
			logger.Debug("已注册应用健康状态相关路由: GET /batch_apps")
		}

		// 应用统计相关路由
		appAnalysisRoutes := v1.Group("/application/apps/analysis")
		{
			logger.Debug("注册应用统计相关路由")
			appAnalysisHandler := app_analysis.NewHandler()
			// 应用类型分析统计
			appAnalysisRoutes.GET("/types", appAnalysisHandler.GetAppAnalysisTypes)
			// 应用状态分析统计
			appAnalysisRoutes.GET("/status", appAnalysisHandler.GetAppAnalysisStatus)
			// 应用标签分析统计
			appAnalysisRoutes.GET("/tags", appAnalysisHandler.GetAppAnalysisTags)
			logger.Debug("已注册应用统计相关路由: GET /types, GET /status, GET /tags")
		}

		// 部门相关路由
		departmentRoutes := v1.Group("/org/departments")
		{
			logger.Debug("注册部门相关路由")
			departmentHandler := department.NewHandler()
			departmentRoutes.POST("", departmentHandler.CreateDepartment)                             // 创建部门
			departmentRoutes.PUT("/:dept_code", departmentHandler.UpdateDepartment)                   // 更新部门
			departmentRoutes.DELETE("/:dept_code", departmentHandler.DeleteDepartment)                // 删除部门
			departmentRoutes.POST("/search", departmentHandler.SearchDepartments)                     // 查询部门列表
			departmentRoutes.POST("/search_by_codes", departmentHandler.SearchDepartmentsByDeptCodes) // 根据部门编码搜索部门
			departmentRoutes.GET("/:dept_code", departmentHandler.GetDepartmentDetail)                // 查询部门详情
			logger.Debug("已注册部门相关路由: POST /, PUT /:dept_code, DELETE /:dept_code, POST /search, GET /:dept_code, POST /custom_import, GET /export")
		}

		// 部门角色相关路由
		departmentRoleRoutes := v1.Group("/org/departments/roles")
		{
			logger.Debug("注册部门角色相关路由")
			departmentRoleHandler := department_role.NewHandler()
			departmentRoleRoutes.POST("", departmentRoleHandler.CreateDepartmentRole)
			departmentRoleRoutes.PUT("/:role_code", departmentRoleHandler.UpdateDepartmentRole)
			departmentRoleRoutes.DELETE("/:role_code", departmentRoleHandler.DeleteDepartmentRole)
			departmentRoleRoutes.POST("/search", departmentRoleHandler.SearchDepartmentRoles)
			departmentRoleRoutes.GET("/:role_code", departmentRoleHandler.GetDepartmentRoleDetail)
			logger.Debug("已注册部门角色相关路由: POST /, PUT /:role_code, DELETE /:role_code, POST /search, GET /:role_code")
		}

		// 部门角色关系相关路由
		departmentRoleRelationRoutes := v1.Group("/org/departments/:dept_code/roles/:role_code")
		{
			logger.Debug("注册部门角色关系相关路由")
			departmentRoleRelationHandler := department_role_relation.NewHandler()
			departmentRoleRelationRoutes.POST("/members", departmentRoleRelationHandler.CreateDepartmentRoleRelation)
			departmentRoleRelationRoutes.POST("/can_select", departmentRoleRelationHandler.CanSelectMembers)
			departmentRoleRelationRoutes.POST("/selected", departmentRoleRelationHandler.SelectedMembers)
			logger.Debug("已注册部门角色关系相关路由: POST /, POST /can_select, POST /selected")
		}

		// 部门成员相关路由
		departmentMemberRoutes := v1.Group("/org/departments/:dept_code/members")
		{
			logger.Debug("注册部门成员相关路由")
			departmentMemberHandler := department_member.NewHandler()
			departmentMemberRoutes.POST("", departmentMemberHandler.CreateMember)
			departmentMemberRoutes.GET("/:user_code", departmentMemberHandler.GetMemberDetail)
			departmentMemberRoutes.PUT("/:user_code", departmentMemberHandler.UpdateMember)
			departmentMemberRoutes.DELETE("/:user_code", departmentMemberHandler.DeleteMember)
			departmentMemberRoutes.POST("/search", departmentMemberHandler.SearchMembers)
			departmentMemberRoutes.POST("/change_dept", departmentMemberHandler.BatchChangeMemberDept)
			departmentMemberRoutes.POST("/invite", departmentMemberHandler.BatchInviteMembers)
			departmentMemberRoutes.POST("/pause", departmentMemberHandler.BatchPauseMembers)

			logger.Debug("已注册部门成员相关路由: POST /, GET /:user_code, PUT /:user_code, DELETE /:user_code, POST /search, POST /change_dept, POST /invite, POST /pause")
		}
		// 组织平台配置相关路由
		orgPlatformConfigRoutes := v1.Group("/org")
		{
			logger.Debug("注册组织平台配置相关路由")
			orgPlatformConfigHandler := org_platform_config.NewHandler()
			orgPlatformConfigRoutes.POST("", orgPlatformConfigHandler.CreateOrgPlatformConfig)                       // 创建组织平台配置
			orgPlatformConfigRoutes.PUT("/:org_code", orgPlatformConfigHandler.UpdateOrgPlatformConfig)              // 更新组织平台配置
			orgPlatformConfigRoutes.DELETE("/:org_code", orgPlatformConfigHandler.DeleteOrgPlatformConfig)           // 删除组织平台配置
			orgPlatformConfigRoutes.POST("/search", orgPlatformConfigHandler.SearchOrgPlatformConfigs)               // 搜索组织平台配置
			orgPlatformConfigRoutes.GET("/:org_code", orgPlatformConfigHandler.GetOrgPlatformConfigDetail)           // 获取组织平台配置详情
			orgPlatformConfigRoutes.POST("/:org_code/sync", orgPlatformConfigHandler.SyncOrgPlatformData)            // 组织平台架构同步
			orgPlatformConfigRoutes.GET("/count", orgPlatformConfigHandler.CountOrgPlatformData)                     // 统计组织的个数
			orgPlatformConfigRoutes.POST("/:org_code/custom_org_import", orgPlatformConfigHandler.CustomOrgImport)   // 组织部门成员-导入
			orgPlatformConfigRoutes.GET("/members/field_mapping", orgPlatformConfigHandler.GetOrgMemberFieldMapping) // 获取组织成员字段映射

			logger.Debug("已注册组织平台配置相关路由: POST /, PUT /:org_code, DELETE /:org_code, POST /search, GET /:org_code")

		}

		certRouter := v1.Group("/application/certs")
		{
			logger.Debug("证书相关路由")

			certHandler := certs.NewHandler()
			certRouter.POST("", certHandler.CreateCert)
			certRouter.GET("/:cert_id", certHandler.GetCert)
			certRouter.PUT("/:cert_id", certHandler.UpdateCert)
			certRouter.DELETE("/:cert_id", certHandler.DeleteCert)
			certRouter.POST("/search", certHandler.ListCerts)

		}

		ipConfigGroup := v1.Group("/application/ip_configs")
		{
			ipConfigHandler := ip_config.NewHandler()
			ipConfigGroup.POST("", ipConfigHandler.CreateIPConfig)
			ipConfigGroup.PUT("/:sign_id", ipConfigHandler.UpdateIPConfig)
			ipConfigGroup.DELETE("/:sign_id", ipConfigHandler.DeleteIPConfig)
			ipConfigGroup.GET("/:sign_id", ipConfigHandler.GetIPConfigDetail)
			ipConfigGroup.POST("/search", ipConfigHandler.SearchIPConfigs)
		}

		orgPlatformMemberRoutes := v1.Group("/org/:org_code")
		{
			logger.Debug("注册组织平台成员相关路由")
			orgPlatformMemberHandler := org_platform_member.NewHandler()

			orgPlatformMemberRoutes.GET("/export/:dept_code", orgPlatformMemberHandler.DepartmentsExport)              // 部门组织导出
			orgPlatformMemberRoutes.GET("/members/count", orgPlatformMemberHandler.CountOrgPlatformMember)             // 统计平台组织的成员数量
			orgPlatformMemberRoutes.POST("/sync_record", orgPlatformMemberHandler.ListSyncRecord)                      // 查看同步记录
			orgPlatformMemberRoutes.GET("/sync_record/:record_code/detail", orgPlatformMemberHandler.SyncRecordDetail) // 查看同步记录详细信息

		}

		// 策略相关路由
		policyRoutes := v1.Group("/policy/")
		{
			logger.Debug("注册策略相关路由")
			policyHandler := policy.NewHandler()
			// 基础策略操作
			policyRoutes.POST("/search", policyHandler.SearchPolicies)               // 查询策略列表
			policyRoutes.POST("", policyHandler.CreatePolicy)                        // 创建策略
			policyRoutes.PUT("/:policy_code", policyHandler.UpdatePolicy)            // 更新策略
			policyRoutes.DELETE("/:policy_code", policyHandler.DeletePolicy)         // 删除策略
			policyRoutes.GET("/:policy_code", policyHandler.GetPolicyDetail)         // 查询策略详情
			policyRoutes.DELETE("/batch", policyHandler.BatchDeletePolicies)         // 批量删除策略
			policyRoutes.GET("/app/:app_code", policyHandler.AppPolicy)              // 应用策略权限分配
			policyRoutes.POST("/app/user_detail", policyHandler.AppPolicyUserDetail) // 应用策略用户

			// 策略状态管理
			policyRoutes.PUT("/:policy_code/status", policyHandler.UpdatePolicyStatus) // 更新策略状态
			// 策略分析
			policyRoutes.GET("/analysis/detail", policyHandler.GetAnalysisDetail) // 策略统计

			// 用户视图相关
			policyRoutes.GET("/user/:user_code/view", policyHandler.UserView)                    // 用户视图
			policyRoutes.GET("/user/:user_code/view/export", policyHandler.UserViewPolicyExport) // 用户视图导出
			policyRoutes.GET("/:policy_code/user_info", policyHandler.GetPolicyUserInfo)         //

			// 导出功能
			policyRoutes.GET("/export", policyHandler.PolicyExport) // 策略导出
			policyRoutes.GET("/list", policyHandler.PolicyInfoList) // 策略详情列表

			policyRoutes.GET("/app_policy/:app_code", policyHandler.AppPolicyList) // 应用策略

			logger.Debug("已注册策略相关路由: POST /search, POST /, PUT /:policy_code, DELETE /:policy_code, GET /:policy_code, GET /analysis/detail, POST /user_view/:user_code, DELETE /batch, POST /:policy_code/status")

			// 场景模板相关路由
			sceneTemplateRoutes := policyRoutes.Group("/scene/template")
			{
				logger.Debug("注册场景模板相关路由")
				sceneTemplateHandler := scene_template.NewHandler()
				sceneTemplateRoutes.POST("", sceneTemplateHandler.CreateSceneTemplate)               // 创建场景模板
				sceneTemplateRoutes.PUT("/:scene_code", sceneTemplateHandler.UpdateSceneTemplate)    // 更新场景模板
				sceneTemplateRoutes.DELETE("/:scene_code", sceneTemplateHandler.DeleteSceneTemplate) // 删除场景模板
				sceneTemplateRoutes.GET("/:scene_code", sceneTemplateHandler.GetSceneTemplateDetail) // 获取场景模板详情
				sceneTemplateRoutes.POST("/search", sceneTemplateHandler.SearchSceneTemplates)       // 搜索场景模板
				sceneTemplateRoutes.DELETE("/batch", sceneTemplateHandler.BatchDeleteSceneTemplates) // 批量删除策略

				logger.Debug("已注册场景模板相关路由: POST /, PUT /:scene_code, DELETE /:scene_code, GET /:scene_code, POST /search")
			}

			policyNetworkRegion := policyRoutes.Group("/network_region")
			{
				logger.Debug("注册网络区域路由")
				policyNetworkRegionHandler := policy_network_region.NewHandler()
				policyNetworkRegion.POST("", policyNetworkRegionHandler.CreatePolicyNetworkRegion)                        // 创建网络区域
				policyNetworkRegion.PUT("/:network_region_code", policyNetworkRegionHandler.UpdatePolicyNetworkRegion)    // 更新网络区域
				policyNetworkRegion.DELETE("/:network_region_code", policyNetworkRegionHandler.DeletePolicyNetworkRegion) // 删除网络区域
				policyNetworkRegion.POST("/search", policyNetworkRegionHandler.SearchPolicyNetworkRegion)                 // 搜索网络区域
			}

			policyProcessGroup := policyRoutes.Group("/process_group")
			{
				logger.Debug("注册策略流程组路由")
				policyProcessGroupHandler := policy_process_group.NewHandler()
				policyProcessGroup.POST("", policyProcessGroupHandler.CreatePolicyProcessGroup)                       // 创建策略流程组
				policyProcessGroup.PUT("/:process_group_code", policyProcessGroupHandler.UpdatePolicyProcessGroup)    // 更新策略流程组
				policyProcessGroup.DELETE("/:process_group_code", policyProcessGroupHandler.DeletePolicyProcessGroup) // 删除策略流程组
				policyProcessGroup.POST("/search", policyProcessGroupHandler.SearchPolicyProcessGroups)               // 搜索策略流程组
			}
		}

		// 用户相关路由
		userRoutes := v1.Group("/org/user")
		{
			logger.Debug("注册用户相关路由")
			userHandler := user.NewHandler()
			userRoutes.POST("/can_select", userHandler.CanSelectMembers) // 搜索用户
			logger.Debug("已注册用户相关路由: POST /can_select")
		}

		userRoleRoutes := v1.Group("/org/user/roles")
		{
			logger.Debug("注册用户角色相关路由")
			userRoleHandler := org_user_role.NewHandler()
			userRoleRoutes.POST("/search_by_codes", userRoleHandler.SearchByRoleCodes) // 根据角色编码搜索用户
			userRoleRoutes.GET("/all", userRoleHandler.GetAllUserRoles)                // 获取所有用户角色
			logger.Debug("已注册用户角色相关路由: POST /search_by_codes, GET /all")
		}

		// 用户组相关路由
		userGroupRoutes := v1.Group("/org/usergroup")
		{
			logger.Debug("注册用户组相关路由")
			userGroupHandler := user_group.NewHandler()
			userGroupRoutes.POST("", userGroupHandler.CreateUserGroup)                            // 创建用户组
			userGroupRoutes.PUT("/:group_code", userGroupHandler.UpdateUserGroup)                 // 更新用户组
			userGroupRoutes.DELETE("/:group_code", userGroupHandler.DeleteUserGroup)              // 删除用户组
			userGroupRoutes.POST("/search", userGroupHandler.SearchUserGroups)                    // 搜索用户组
			userGroupRoutes.GET("/:group_code", userGroupHandler.GetUserGroupDetail)              // 获取用户组详情
			userGroupRoutes.POST("/search_by_codes", userGroupHandler.SearchUserGroupByCodes)     // 根据用户组编码搜索用户组
			userGroupRoutes.GET("/user_code/:user_code", userGroupHandler.GetUserGroupByUserCode) // 根据用户编码搜索用户组
			logger.Debug("已注册用户组相关路由: POST /, PUT /:group_code, DELETE /:group_code, POST /search, GET /:group_code, POST /search_by_codes")
		}

		// 用户组关系相关路由
		userGroupRelationRoutes := v1.Group("/org/usergroup/:group_code/members")
		{
			logger.Debug("注册用户组关系相关路由")
			userGroupRelationHandler := user_group_relation.NewHandler()
			userGroupRelationRoutes.POST("", userGroupRelationHandler.CreateUserGroupRelation)         // 创建用户组关系
			userGroupRelationRoutes.DELETE("", userGroupRelationHandler.DeleteUserGroupRelation)       // 删除用户组关系
			userGroupRelationRoutes.POST("/can_select", userGroupRelationHandler.CanSelectMembers)     // 查询可选择成员
			userGroupRelationRoutes.POST("/selected", userGroupRelationHandler.SelectedMembers)        // 查询已选择成员
			userGroupRelationRoutes.POST("/search", userGroupRelationHandler.SearchUserGroupRelations) // 搜索用户组关系
			userGroupRelationRoutes.GET("/export", userGroupRelationHandler.UserGroupExport)           // 搜索用户组关系
			userGroupRelationRoutes.POST("/import_members", userGroupRelationHandler.ImportMembers)    // 导入成员
			userGroupRelationRoutes.GET("/:user_code", userGroupRelationHandler.GetUserGroupUserInfo)  // 获取用户组用户信息

			logger.Debug("已注册用户组关系相关路由: POST /, DELETE /, POST /can_select, POST /selected, POST /search, GET /export")
		}
		userGroupRelationNewRoutes := v1.Group("/org/usergroup/members")
		{
			userGroupRelationHandler := user_group_relation.NewHandler()
			userGroupRelationNewRoutes.POST("/batch_export", userGroupRelationHandler.BatchUserGroupExport) // 搜索用户组关系
		}

		// 单独生成服务 connector-cluster-service
		// // 连接器集群相关路由
		// connectorClusterRoutes := v1.Group("/connectors/clusters")
		// {
		// 	logger.Debug("注册连接器集群相关路由")
		// 	connectorClusterHandler := connector_cluster.NewHandler()
		// 	connectorClusterRoutes.POST("", connectorClusterHandler.CreateConnectorCluster)                        // 创建连接器集群
		// 	connectorClusterRoutes.POST("/all", connectorClusterHandler.GetAllClusterSimpleInfo)                   // 获取所有集群的精简信息
		// 	connectorClusterRoutes.GET("/statistics", connectorClusterHandler.GetClusterStatistics)                // 获取连接器集群统计信息
		// 	connectorClusterRoutes.POST("/search", connectorClusterHandler.SearchConnectorClusters)                // 搜索连接器集群
		// 	connectorClusterRoutes.POST("/delete", connectorClusterHandler.BatchDeleteConnectorClusters)           // 批量删除连接器集群
		// 	connectorClusterRoutes.POST("/cluster_names", connectorClusterHandler.GetClusterNamesByClusterCodes)   // 获取集群名称列表
		// 	connectorClusterRoutes.GET("/:cluster_code", connectorClusterHandler.GetConnectorClusterDetail)        // 获取连接器集群详情
		// 	connectorClusterRoutes.PUT("/:cluster_code", connectorClusterHandler.UpdateConnectorCluster)           // 更新连接器集群
		// 	connectorClusterRoutes.DELETE("/:cluster_code", connectorClusterHandler.DeleteConnectorCluster)        // 删除连接器集群
		// 	connectorClusterRoutes.POST("/:cluster_code/restore", connectorClusterHandler.RestoreConnectorCluster) // 恢复连接器集群
		// 	connectorClusterRoutes.GET("/:cluster_code/connectors", connectorClusterHandler.ListClusterConnectors) // 获取集群内连接器列表
		// 	logger.Debug("已注册连接器集群相关路由: POST /, GET /:cluster_code, POST /search, PUT /:cluster_code, DELETE /:cluster_code, POST /delete, POST /:cluster_code/restore, GET /:cluster_code/statistics, GET /:cluster_code/connectors, POST /cluster_names")
		// }

		// // 连接器相关路由
		// connectorInfosRoutes := v1.Group("/connectors/infos")
		// {
		// 	logger.Debug("注册连接器相关路由")
		// 	connectorHandler := connector.NewHandler()
		// 	connectorInfosRoutes.POST("", connectorHandler.CreateConnector)                                                      // 创建连接器
		// 	connectorInfosRoutes.POST("/search", connectorHandler.SearchConnectors)                                              // 搜索连接器
		// 	connectorInfosRoutes.DELETE("/:connector_code", connectorHandler.DeleteConnector)                                    // 删除连接器
		// 	connectorInfosRoutes.PUT("/:connector_code/used_status", connectorHandler.UpdateConnectorUsedStatus)                 // 更新连接器状态
		// 	connectorInfosRoutes.PUT("/:connector_code/description", connectorHandler.UpdateConnectorDescription)                // 更新连接器描述
		// 	connectorInfosRoutes.PUT("/:connector_code/bandwidth_threshold", connectorHandler.UpdateConnectorBandwidthThreshold) // 更新连接器带宽阈值
		// 	connectorInfosRoutes.PUT("/used_status", connectorHandler.BatchUpdateConnectorUsedStatus)                            // 批量更新连接器状态
		// 	connectorInfosRoutes.POST("/delete", connectorHandler.BatchDeleteConnectors)                                         // 批量删除连接器
		// 	// connectorInfosRoutes.PUT("/:connector_code", connectorHandler.UpdateConnector)              // 更新连接器
		// 	// connectorInfosRoutes.GET("/:connector_code", connectorHandler.GetConnectorDetail)           // 获取连接器详情
		// 	logger.Debug("已注册连接器相关路由: POST /, GET /:connector_code, POST /search, PUT /:connector_code, DELETE /:connector_code, PUT /:connector_code/status")

		// }

		// 设备分组相关路由
		deviceGroupRoutes := v1.Group("/device/groups")
		{
			logger.Debug("注册设备分组相关路由")
			deviceGroupHandler := device_group.NewHandler()
			deviceGroupRoutes.POST("", deviceGroupHandler.CreateDeviceGroup)
			deviceGroupRoutes.PUT("/:group_code/name", deviceGroupHandler.UpdateDeviceGroupName)
			deviceGroupRoutes.PUT("/:group_code/condition", deviceGroupHandler.UpdateDeviceGroupCondition)
			deviceGroupRoutes.DELETE("/:group_code", deviceGroupHandler.DeleteDeviceGroup)
			deviceGroupRoutes.GET("/:group_code", deviceGroupHandler.GetDeviceGroupDetail)
			deviceGroupRoutes.POST("/search", deviceGroupHandler.SearchDeviceGroups)
			logger.Debug("已注册设备分组相关路由: POST /, PUT /:group_code/name, PUT /:group_code/condition, DELETE /:group_code, GET /:group_code, POST /search")
		}

		// ZTNA日志相关路由
		ztnaLogRoutes := v1.Group("/logs")
		{
			logger.Debug("注册ZTNA日志相关路由")
			ztnaLogHandler := ztna_log.NewHandler()
			ztnaLogRoutes.GET("/ztna", ztnaLogHandler.SearchZtnaLogs)           // 搜索ZTNA日志
			ztnaLogRoutes.GET("/ztna/export", ztnaLogHandler.ExportZtnaLogs)    // 导出ZTNA日志
			ztnaLogRoutes.GET("/ztna/:log_id", ztnaLogHandler.GetZtnaLogDetail) // 获取ZTNA日志详情
			logger.Debug("已注册ZTNA日志相关路由: GET /ztna, GET /ztna/export, GET /ztna/:log_id")

			// 权限申请日志
			applyPermissionLogHandler := apply_permission_log.NewHandler()
			ztnaLogRoutes.GET("/apply_permission", applyPermissionLogHandler.SearchApplyPermissionLogs) // 搜索权限申请日志
			logger.Debug("已注册权限申请日志相关路由: GET /apply_permission")
		}

		notifyPolicyRoutes := v1.Group("/notify_policy")
		{
			logger.Debug("注册通知策略相关路由")
			notifyPolicyHandler := notify_policy.NewHandler()
			notifyPolicyRoutes.POST("/get_list", notifyPolicyHandler.GetList)
			notifyPolicyRoutes.POST("/add_policy", notifyPolicyHandler.AddPolicy)
			notifyPolicyRoutes.POST("/update_status", notifyPolicyHandler.UpdateStatus)
			notifyPolicyRoutes.POST("/delete", notifyPolicyHandler.Delete)
			notifyPolicyRoutes.POST("/update", notifyPolicyHandler.Update)
		}

		notifyRoutes := v1.Group("/notify")
		{
			logger.Debug("注册通知消息相关路由")
			notifyHandler := notify.NewHandler()
			notifyRoutes.POST("/get_message_list", notifyHandler.GetMessageList)
			notifyRoutes.POST("/get_im_setting_list", notifyHandler.GetImSettingList)
			notifyRoutes.POST("/add_setting", notifyHandler.NotifyAddSetting)
			notifyRoutes.POST("/send_test_message", notifyHandler.SendTestMessage)
			notifyRoutes.POST("/save_status_or_delete", notifyHandler.SaveStatueOrDelete)
			notifyRoutes.POST("/update", notifyHandler.Update)
		}

		deployRoutes := v1.Group("/deploy")
		{
			logger.Debug("注册部署相关路由")
			deployHandler := deploy.NewHandler()
			deployRoutes.GET("/get_deploy_info", deployHandler.GetDeployInfo)
			deployRoutes.POST("/upsert_deploy_info", deployHandler.UpsertDeployInfo)
			deployRoutes.GET("/download_packages", deployHandler.GetDownloadPackages) // 获取部署下载包列表
		}

		deployInviteRoutes := v1.Group("/deploy_invite")
		{
			logger.Debug("注册部署邀请相关路由")
			deployInviteHandler := deploy.NewHandler()
			deployInviteRoutes.GET("/:code", deployInviteHandler.DeployInvite)
		}

		// 企业设置相关路由
		enterpriseSettingRoutes := v1.Group("/enterprise/settings")
		{
			logger.Debug("注册企业设置相关路由")
			enterpriseSettingHandler := enterprise_setting.NewHandler()
			enterpriseSettingRoutes.POST("", enterpriseSettingHandler.CreateOrUpdateEnterpriseSetting) // 创建或更新企业设置
			enterpriseSettingRoutes.PUT("", enterpriseSettingHandler.UpdateEnterpriseSetting)          // 更新企业设置
			enterpriseSettingRoutes.GET("", enterpriseSettingHandler.GetEnterpriseSettings)            // 获取企业设置列表
			enterpriseSettingRoutes.GET("/:type", enterpriseSettingHandler.GetEnterpriseSetting)       // 获取特定类型的企业设置
			logger.Debug("已注册企业设置相关路由: POST /, PUT /, GET /, GET /:type")
		}

	}

	logger.Info("API路由注册完成")
}
