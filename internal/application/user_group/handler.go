package user_group

import (
	"context"
	"encoding/json"
	"regexp"
	"time"

	"sase_cloud_api/internal/common"
	"sase_cloud_api/internal/common/errors"
	"sase_cloud_api/internal/dto"
	"sase_cloud_api/internal/model"
	"sase_cloud_api/internal/model/department"
	"sase_cloud_api/internal/model/user_group"
	"sase_cloud_api/internal/model/user_group_relation"
	"sase_cloud_api/internal/util"
	"sase_cloud_api/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

type Handler struct {
}

func NewHandler() *Handler {
	return &Handler{}
}

// convertToResponse 将模型转换为响应DTO
func convertToResponse(group *user_group.UserGroup) dto.UserGroupResponse {
	return dto.UserGroupResponse{
		GroupCode:        group.GroupCode,
		GroupName:        group.GroupName,
		Conditions:       group.Conditions,
		InvalidUserCodes: group.InvalidUserCodes,
	}
}

// CreateUserGroup 创建用户组，根据分组条件将用户添加到用户组
func (h *Handler) CreateUserGroup(c *gin.Context) {
	var req dto.CreateUserGroupRequest
	req.ProductID = c.GetString("product_id")
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("CreateUserGroup error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 检查用户组名称是否已存在
	exists, err := model.UserGroupRepo.CheckGroupNameExists(c.Request.Context(), req.ProductID, req.GroupName, "")
	if err != nil {
		logger.Errorf("CheckGroupNameExists error: %v", err)
		common.ResponseResult(c, errors.AddError)
		return
	}
	if exists {
		common.ResponseResult(c, errors.GroupNameAlreadyExists)
		return
	}

	groupCode, err := util.Sequence.GenerateGroupCode()
	if err != nil {
		logger.Errorf("生成用户组编码失败: %v", err)
		common.ResponseResult(c, errors.AddError, "生成用户组编码失败")
		return
	}
	group := &user_group.UserGroup{
		ProductID:        req.ProductID,
		GroupCode:        groupCode,
		GroupName:        req.GroupName,
		Conditions:       req.Conditions,
		InvalidUserCodes: req.InvalidUserCodes,
	}

	if err := model.UserGroupRepo.CreateUserGroup(c.Request.Context(), group); err != nil {
		logger.Errorf("CreateUserGroup error: %v", err)
		common.ResponseResult(c, errors.AddError)
		return
	}

	go addUserToUserGroupByConditions(req.ProductID, group.GroupCode, req.Conditions, req.InvalidUserCodes)

	common.ResponseResult(c, convertToResponse(group))
}

// UpdateUserGroup 更新用户组
func (h *Handler) UpdateUserGroup(c *gin.Context) {
	var req dto.UpdateUserGroupRequest
	req.ProductID = c.GetString("product_id")
	req.GroupCode = c.Param("group_code")

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("UpdateUserGroup error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	// 检查用户组名称是否已存在（排除当前用户组）
	exists, err := model.UserGroupRepo.CheckGroupNameExists(c.Request.Context(), req.ProductID, req.GroupName, req.GroupCode)
	if err != nil {
		logger.Errorf("CheckGroupNameExists error: %v", err)
		common.ResponseResult(c, errors.UpdateError)
		return
	}
	if exists {
		common.ResponseResult(c, errors.GroupNameAlreadyExists)
		return
	}

	updateGroup := &user_group.UserGroup{
		GroupName:        req.GroupName,
		Conditions:       req.Conditions,
		InvalidUserCodes: req.InvalidUserCodes,
	}

	if err := model.UserGroupRepo.UpdateUserGroup(c.Request.Context(), req.ProductID, req.GroupCode, updateGroup); err != nil {
		logger.Errorf("UpdateUserGroup error: %v", err)
		common.ResponseResult(c, errors.UpdateError)
		return
	}

	group, err := model.UserGroupRepo.GetUserGroupDetail(c.Request.Context(), req.ProductID, req.GroupCode)
	if err != nil {
		logger.Errorf("get UpdateUserGroup error: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}

	go addUserToUserGroupByConditions(req.ProductID, group.GroupCode, req.Conditions, req.InvalidUserCodes)

	common.ResponseResult(c, convertToResponse(group))
}

// DeleteUserGroup 删除用户组
func (h *Handler) DeleteUserGroup(c *gin.Context) {
	var req dto.DeleteUserGroupRequest
	req.ProductID = c.GetString("product_id")
	req.GroupCode = c.Param("group_code")

	if err := req.Validate(); err != nil {
		logger.Errorf("DeleteUserGroup Validate error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	if err := model.UserGroupRepo.DeleteUserGroup(c.Request.Context(), req.ProductID, req.GroupCode); err != nil {
		logger.Errorf("DeleteUserGroup error: %v", err)
		common.ResponseResult(c, errors.DeleteError)
		return
	}

	// 删除用户组关系
	err := model.UserGroupRelationRepo.DeleteAllRelationsByGroupCode(c.Request.Context(), req.ProductID, req.GroupCode)
	if err != nil {
		logger.Errorf("DeleteUserGroup delete all relations by group code error: %v", err)
		common.ResponseResult(c, errors.DeleteError)
		return
	}

	common.ResponseResult(c, nil)
}

// SearchUserGroups 搜索用户组
func (h *Handler) SearchUserGroups(c *gin.Context) {
	var req dto.SearchUserGroupRequest
	req.ProductID = c.GetString("product_id")

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("SearchUserGroups error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	groups, total, err := model.UserGroupRepo.SearchUserGroups(c.Request.Context(), req.ProductID, req.GroupName, req.Page, req.Limit)
	if err != nil {
		logger.Errorf("SearchUserGroups error: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}

	response := dto.SearchUserGroupResponse{
		Total: int(total),
		Items: make([]dto.UserGroupResponse, len(groups)),
	}

	for i, group := range groups {
		response.Items[i] = convertToResponse(group)
	}

	common.ResponseResult(c, response)
}

// 根据用户组编码搜索用户
func (h *Handler) SearchUserGroupByCodes(c *gin.Context) {
	var req dto.SearchUserGroupByCodesRequest
	req.ProductID = c.GetString("product_id")

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("SearchUserGroupByCodes Validate error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	groups, err := model.UserGroupRepo.SearchUserGroupByCodes(c.Request.Context(), req.ProductID, req.GroupCodes)
	if err != nil {
		logger.Errorf("SearchUserGroupByCodes error: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}

	response := dto.SearchUserGroupByCodesResponse{
		Total: int(len(groups)),
		Items: make([]dto.UserGroupResponse, len(groups)),
	}

	for i, group := range groups {
		response.Items[i] = convertToResponse(group)
	}

	common.ResponseResult(c, response)
}

// GetUserGroupByUserCode 根据用户编码搜索用户组
func (h *Handler) GetUserGroupByUserCode(c *gin.Context) {
	var req dto.GetUserGroupByUserCodeRequest
	req.ProductID = c.GetString("product_id")
	req.UserCode = c.Param("user_code")

	if err := req.Validate(); err != nil {
		logger.Errorf("GetUserGroupByUserCode Validate error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	userGroups, err := model.UserGroupRelationRepo.GetUserGroups(c.Request.Context(), req.ProductID, req.UserCode)
	if err != nil {
		logger.Errorf("GetUserGroupByUserCode error: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}
	response := dto.GetUserGroupByUserCodeResponse{
		Total: int(len(userGroups)),
		Items: userGroups,
	}

	common.ResponseResult(c, response)
}

// GetUserGroupDetail 获取用户组详情
func (h *Handler) GetUserGroupDetail(c *gin.Context) {
	var req dto.GetUserGroupDetailRequest
	req.GroupCode = c.Param("group_code")
	req.ProductID = c.GetString("product_id")

	if err := req.Validate(); err != nil {
		logger.Errorf("GetUserGroupDetail Validate error: %v", err)
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	group, err := model.UserGroupRepo.GetUserGroupDetail(c.Request.Context(), req.ProductID, req.GroupCode)
	if err != nil {
		logger.Errorf("GetUserGroupDetail error: %v", err)
		common.ResponseResult(c, errors.SelectError)
		return
	}
	if group == nil {
		logger.Errorf("GetUserGroupDetail error: group not found")
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	var invalidUserInfo []dto.InvalidUserInfo
	if len(group.InvalidUserCodes) > 0 {
		users, err := model.UserRepo.FindMany(c, bson.M{"product_id": req.ProductID, "user_code": bson.M{"$in": group.InvalidUserCodes}}, nil)
		if err != nil {
			logger.Errorf("获取用户信息失败: %v", err)
			common.ResponseResult(c, errors.SelectError)
			return
		}

		var orgCodes, deptCodes []string
		for _, item := range users {
			orgCodes = append(orgCodes, item.OrgCode)
			for _, val := range item.DeptPath {
				deptCodes = append(deptCodes, val...)
			}
		}

		organizationsNameMap := make(map[string]string)
		if len(orgCodes) > 0 {
			organizations, err := model.OrgPlatformConfigRepo.FindMany(c, bson.M{"product_id": req.ProductID, "org_code": bson.M{"$in": orgCodes}}, nil)
			if err != nil {
				logger.Errorf("请求组织失败1: %v", err)
				common.ResponseResult(c, errors.SelectError, err.Error())
				return
			}

			for _, item := range organizations {
				organizationsNameMap[item.OrgCode] = item.OrgName
			}
		}

		departmentMaps := make(map[string]*department.Department)

		if len(deptCodes) > 0 {
			departments, err := model.DepartmentRepo.FindMany(c, bson.M{"product_id": req.ProductID, "status": 1, "dept_code": bson.M{"$in": deptCodes}}, nil)
			if err != nil {
				logger.Errorf("获取用户部门失败: %v", err)
				common.ResponseResult(c, errors.SelectError)
				return
			}
			for _, item := range departments {
				departmentMaps[item.DeptCode] = item
			}
		}

		for _, item := range users {
			var userDeptPath [][]dto.DeptPath
			for _, val := range item.DeptPath {
				deptPathInfo := make([]dto.DeptPath, 0)
				for _, v := range val {
					if info, ok := departmentMaps[v]; ok {
						deptPathInfo = append(deptPathInfo, dto.DeptPath{
							DeptCode: info.DeptCode,
							DeptName: info.DeptName,
						})
					}
				}
				userDeptPath = append(userDeptPath, deptPathInfo)
			}

			invalidUserInfo = append(invalidUserInfo, dto.InvalidUserInfo{
				UserCode: item.UserCode,
				UserName: item.UserName,
				DeptPath: userDeptPath,
				OrgCode:  item.OrgCode,
				OrgName:  organizationsNameMap[item.OrgCode],
			})
		}
	}

	data := dto.GetUserGroupDetailResponse{
		GroupCode:       group.GroupCode,
		GroupName:       group.GroupName,
		Conditions:      group.Conditions,
		InvalidUserInfo: invalidUserInfo,
	}

	common.ResponseResult(c, data)
}

// 根据条件添加用户到用户组
func addUserToUserGroupByConditions(productID string, groupCode string, Conditions []dto.Condition, invalidUserCodes []string) error {
	userFilter, err := generateUserFilterByConditions(productID, Conditions, invalidUserCodes)
	if err != nil {
		logger.Errorf("addUserToUserGroupByConditions generate user filter error: %v", err)
		return err
	}

	userList, err := model.UserRepo.FindMany(context.Background(), userFilter, nil)
	if err != nil {
		logger.Errorf("addUserToUserGroupByConditions find user error: %v", err)
		return err
	}
	userCodes := make([]string, 0)
	for _, user := range userList {
		userCodes = append(userCodes, user.UserCode)
	}

	// 如果userodes 数量过多，就分批操作
	batchSize := 1000
	for i := 0; i < len(userCodes); i += batchSize {
		batchUserCodes := userCodes[i:min(i+batchSize, len(userCodes))]

		// 检查用户是否已经在用户组中
		existsUserGroupRelations, err := model.UserGroupRelationRepo.GetGroupUserRelationsByUserCodes(context.Background(), productID, batchUserCodes, groupCode)
		if err != nil {
			logger.Errorf("addUserToUserGroupByConditions get group user relations by user codes error: %v", err)
			return err
		}
		existsUserCodes := make(map[string]bool)
		for _, relation := range existsUserGroupRelations {
			existsUserCodes[relation.UserCode] = true
		}
		notExistsUserCodes := make([]string, 0)
		for _, userCode := range batchUserCodes {
			if _, ok := existsUserCodes[userCode]; !ok {
				notExistsUserCodes = append(notExistsUserCodes, userCode)
			}
		}

		if len(notExistsUserCodes) == 0 { // 没有需要创建的用户组关系
			continue
		}

		// 创建用户组关系
		relations := make([]*user_group_relation.UserGroupRelationCreate, 0)
		for _, userCode := range notExistsUserCodes {
			relations = append(relations, &user_group_relation.UserGroupRelationCreate{
				ProductID: productID,
				UserCode:  userCode,
				GroupCode: groupCode,
				AddType:   "2",
				CreatedAt: time.Now().Unix(),
				UpdatedAt: time.Now().Unix(),
			})
		}
		err = model.UserGroupRelationRepo.BatchCreateRelation(context.Background(), relations)
		if err != nil {
			logger.Errorf("addUserToUserGroupByConditions batch create relation error: %v", err)
			return err
		}
	}

	return nil
}

// 根据条件生成用户过滤器
func generateUserFilterByConditions(productID string, Conditions []dto.Condition, invalidUserCodes []string) (bson.M, error) {
	conditions := make([]bson.M, 0)
	for _, condition := range Conditions {
		switch condition.Key {
		case "user_position": // organization_user 表 position
			cond := generateMongoQuery(condition.Operator, condition.Val)
			if cond == nil {
				logger.Errorf("generateUserFilterByConditions user_position condition error: %v", condition)
				return nil, errors.InvalidParameter
			}
			conditions = append(conditions, bson.M{"position": cond})
		case "mobile": // organization_user 表 mobile
			cond := generateMongoQuery(condition.Operator, condition.Val)
			if cond == nil {
				logger.Errorf("generateUserFilterByConditions mobile condition error: %v", condition)
				return nil, errors.InvalidParameter
			}
			conditions = append(conditions, bson.M{"mobile": cond})
		case "email": // organization_user 表 email
			cond := generateMongoQuery(condition.Operator, condition.Val)
			if cond == nil {
				logger.Errorf("generateUserFilterByConditions email condition error: %v", condition)
				return nil, errors.InvalidParameter
			}
			conditions = append(conditions, bson.M{"email": cond})
		case "department": // organization_user 表 dept_path
			departmentList := make([]string, 0)
			departmentMap := make([]map[string]string, 0)
			err := json.Unmarshal([]byte(condition.Val), &departmentMap)
			if err != nil {
				logger.Errorf("generateUserFilterByConditions department json unmarshal error: %v", err)
				return nil, err
			}
			for _, department := range departmentMap {
				if val, ok := department["dept_code"]; ok {
					departmentList = append(departmentList, val)
				}
			}
			if len(departmentList) > 0 {
				if condition.Operator == "inarr" {
					conditions = append(conditions, bson.M{"dept_path": bson.M{
						"$elemMatch": bson.M{
							"$elemMatch": bson.M{
								"$in": departmentList,
							},
						},
					}})
				} else if condition.Operator == "notinarr" {
					conditions = append(conditions, bson.M{"dept_path": bson.M{
						"$elemMatch": bson.M{
							"$elemMatch": bson.M{
								"$nin": departmentList,
							},
						},
					}})
				} else {
					logger.Errorf("generateUserFilterByConditions department operator error: %v", condition.Operator)
					return nil, errors.InvalidParameter
				}
			}
		case "user_role": // organization_user 表 user_roles
			userRoleList := make([]string, 0)
			userRoleMap := make([]map[string]string, 0)
			err := json.Unmarshal([]byte(condition.Val), &userRoleMap)
			if err != nil {
				logger.Errorf("generateUserFilterByConditions user_role json unmarshal error: %v", err)
				return nil, err
			}
			for _, userRole := range userRoleMap {
				if val, ok := userRole["role_code"]; ok {
					userRoleList = append(userRoleList, val)
				}
			}

			if len(userRoleList) > 0 {
				if condition.Operator == "inarr" {
					conditions = append(conditions, bson.M{"user_roles": bson.M{"$in": userRoleList}})
				} else if condition.Operator == "notinarr" {
					conditions = append(conditions, bson.M{"user_roles": bson.M{"$nin": userRoleList}})
				} else {
					logger.Errorf("generateUserFilterByConditions user_role operator error: %v", condition.Operator)
					return nil, errors.InvalidParameter
				}
			}

		case "department_name": // organization_department 表
			// 通过name来查询dept_code
			deptNameCondition := generateMongoQuery(condition.Operator, condition.Val)
			if deptNameCondition == nil {
				logger.Errorf("generateUserFilterByConditions department name deptNameCondition error: %v", errors.InvalidParameter)
				return nil, errors.InvalidParameter
			}
			filter := bson.M{"product_id": productID, "dept_name": deptNameCondition}
			dept, err := model.DepartmentRepo.FindMany(context.Background(), filter, nil)
			if err != nil {
				logger.Errorf("generateUserFilterByConditions department name find dept_code error: %v, filter: %v", err, filter)
				return nil, err
			}
			deptCodes := make([]string, 0)
			for _, d := range dept {
				deptCodes = append(deptCodes, d.DeptCode)
			}
			if len(deptCodes) == 0 { // 没有找到部门
				logger.Errorf("generateUserFilterByConditions department name not find dept_code, filter: %v", filter)
				return nil, errors.InvalidParameter
			}

			conditions = append(conditions, bson.M{"dept_path": bson.M{
				"$elemMatch": bson.M{
					"$elemMatch": bson.M{
						"$in": deptCodes,
					},
				},
			}})

		case "user_role_name": // organization_role 表 通过name 转换为 role_code
			roleNameCondition := generateMongoQuery(condition.Operator, condition.Val)
			if roleNameCondition == nil {
				logger.Errorf("generateUserFilterByConditions user_role_name roleNameCondition error, operator: %v, value: %v", condition.Operator, condition.Val)
				return nil, errors.InvalidParameter
			}

			filter := bson.M{"product_id": productID, "role_name": roleNameCondition}
			role, err := model.OrganizationRoleRepo.SearchRoles(context.Background(), filter)
			if err != nil {
				logger.Errorf("generateUserFilterByConditions user_role_name find role_code error: %v, filter: %v", err, filter)
				return nil, err
			}
			roleCodes := make([]string, 0)
			for _, r := range role {
				roleCodes = append(roleCodes, r.RoleCode)
			}
			if len(roleCodes) == 0 { // 没有找到角色
				logger.Errorf("generateUserFilterByConditions user_role_name not find role_code, filter: %v", filter)
				return nil, errors.InvalidParameter
			}

			conditions = append(conditions, bson.M{"user_roles": bson.M{"$in": roleCodes}})

		}

	}
	if len(conditions) == 0 {
		logger.Errorf("generateUserFilterByConditions conditions is empty")
		return nil, errors.InvalidParameter
	}

	if len(invalidUserCodes) > 0 {
		conditions = append(conditions, bson.M{"user_code": bson.M{"$nin": invalidUserCodes}})
	}

	userFilter := bson.M{
		"product_id": productID,
		"$and":       conditions,
	}

	return userFilter, nil
}

// 根据运算符生成对应的mongo查询条件，支持：hasstr, nothasstr, match, not_match, eq, neq
func generateMongoQuery(operator string, value string) bson.M {
	switch operator {
	case "hasstr":
		return bson.M{"$regex": regexp.QuoteMeta(value), "$options": "i"}
	case "nothasstr":
		return bson.M{"$not": bson.M{"$regex": regexp.QuoteMeta(value), "$options": "i"}}
	case "match":
		return bson.M{"$regex": regexp.QuoteMeta(value), "$options": "i"}
	case "not_match":
		return bson.M{"$not": bson.M{"$regex": regexp.QuoteMeta(value), "$options": "i"}}
	case "eq":
		return bson.M{"$eq": value}
	case "neq":
		return bson.M{"$ne": value}
	}
	return nil
}
