package org_platform_config

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"github.com/spf13/cast"
	"sase_cloud_api/pkg/logger"
	"sase_cloud_api/pkg/utils/third_party"
)

// SyncStatus 定义同步状态枚举
type SyncStatus int64

const (
	NOT_SYNCED SyncStatus = 0 // 未同步
	SYNCING    SyncStatus = 1 // 同步中
	SYNCED     SyncStatus = 2 // 已同步
	FAILED     SyncStatus = 3 // 同步失败
)

// SourceType 定义来源类型枚举
type SourceType string

const (
	DINGTALK SourceType = "DINGTALK" // 钉钉
	WECHAT   SourceType = "WECOM"    // 企业微信
	FEISHU   SourceType = "FEISHU"   // 飞书
	CUSTOM   SourceType = "CUSTOM"   //自定义组织
	LDAP     SourceType = "LDAP"
)

var SourceMapping = map[SourceType]third_party.PlatformType{
	DINGTALK: third_party.PlatformTypeDingTalk,
	WECHAT:   third_party.PlatformTypeWecom,
	FEISHU:   third_party.PlatformTypeFeishu,
	LDAP:     third_party.PlatformTypeLDAP,
}

// SourceConfig 组织平台的源配置
type SourceConfig map[string]interface{}

// Value 实现driver.Valuer接口
func (c SourceConfig) Value() (driver.Value, error) {
	if c == nil {
		return nil, nil
	}
	bytes, err := json.Marshal(c)
	return string(bytes), err
}

// Scan 实现sql.Scanner接口
func (c *SourceConfig) Scan(value interface{}) error {
	if value == nil {
		*c = make(SourceConfig)
		return nil
	}
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("类型错误")
	}
	return json.Unmarshal(bytes, c)
}
func (c *SourceConfig) GetFiledMapping() map[string]interface{} {
	mapData := make(map[string]interface{})
	var fileMap = &FieldMap{}

	v, _ := c.Value()
	if v == nil {
		return mapData
	}
	err := json.Unmarshal([]byte(cast.ToString(v)), &fileMap)
	if err != nil {
		logger.Warnf("failed to decode error: %s", err)
		return mapData
	}
	for _, filed := range fileMap.SyncConfig.MappingFields {
		mapData[filed.SystemField] = filed.ThirdPartyField
	}
	return mapData
}

// 获取显示的字段
func (c *SourceConfig) GetDisplayName() []string {
	mapData := make([]string, 0)
	var fileMap = &FieldMap{}

	v, _ := c.Value()
	if v == nil {
		return mapData
	}
	err := json.Unmarshal([]byte(cast.ToString(v)), &fileMap)
	if err != nil {
		logger.Warnf("failed to decode error: %s", err)
		return mapData
	}
	for _, filed := range fileMap.SyncConfig.DisplayName {
		mapData = append(mapData, filed)
	}
	return mapData
}

type FieldMap struct {
	SyncConfig struct {
		DisplayName   []string `json:"display_name"`
		MappingFields []struct {
			SystemField     string `json:"system_field"`
			SystemFieldText string `json:"system_field_text"`
			ThirdPartyField string `json:"third_party_field"`
		} `json:"mapping_fields"`
	} `json:"sync_config"`
}

// OrgPlatformConfig 组织平台配置模型
type OrgPlatformConfig struct {
	ID                string       `bson:"_id,omitempty" json:"-"`
	OrgCode           string       `bson:"org_code" json:"org_code"`
	OrgName           string       `bson:"org_name" json:"org_name"`
	SourceType        SourceType   `bson:"source_type" json:"source_type"`
	SourceConfig      SourceConfig `bson:"source_config" json:"source_config"`
	ProductID         string       `bson:"product_id" json:"product_id"`
	SyncStatus        SyncStatus   `bson:"sync_status" json:"sync_status"`
	LastSyncTime      int64        `bson:"last_sync_time" json:"last_sync_time"`
	LastUpdateBatchNo string       `bson:"last_update_batch_no" json:"last_update_batch_no"`
	Status            int          `bson:"status" json:"status"`
	CreatedAt         int64        `bson:"created_at" json:"-"`
	UpdatedAt         int64        `bson:"updated_at" json:"-"`
	AutoSync          int64        `json:"auto_sync" bson:"auto_sync"`
	CorpId            string       `json:"corp_id" bson:"corp_id"`
	AppKey            string       `json:"app_key" bson:"app_key"`
	Secret            string       `json:"secret" bson:"secret"`
	RangeType         int64        `json:"range_type" bson:"range_type"`
	AutoSyncTime      int64        `json:"auto_sync_time" bson:"auto_sync_time"` // 使用秒的数据进行标记
	SyncMode          string       `json:"sync_mode" bson:"sync_mode"`
	CreateBy          string       `json:"create_by" bson:"create_by"`
	UpdateBy          string       `json:"update_by" bson:"update_by"`
	ThirdAuthStatus   bool         `json:"third_auth_status" bson:"third_auth_status"` // 快捷登录的开关
	LdapConfig        *LdapConfig  `json:"ldap_config" bson:"ldap_config"`
}

type LdapConfig struct {
	LdapURL      string `json:"ldap_url" bson:"ldap_url"`
	LdapUsername string `json:"ldap_username" bson:"ldap_username"`
	LdapPassword string `json:"ldap_password" bson:"ldap_password"`
	LdapDN       string `json:"ldap_dn" bson:"ldap_dn"`
}

func (o *OrgPlatformConfig) GetLdapURL() string {
	if o.LdapConfig != nil {
		return o.LdapConfig.LdapURL
	}
	return ""
}

func (o *OrgPlatformConfig) GetLdapUsername() string {
	if o.LdapConfig != nil {
		return o.LdapConfig.LdapUsername
	}
	return ""
}

func (o *OrgPlatformConfig) GetLdapPassword() string {
	if o.LdapConfig != nil {
		return o.LdapConfig.LdapPassword
	}
	return ""
}
func (o *OrgPlatformConfig) GetLdapDN() string {
	if o.LdapConfig != nil {
		return o.LdapConfig.LdapDN
	}
	return ""
}

func (o *OrgPlatformConfig) GetFiledMapping() map[string]interface{} {

	return o.SourceConfig.GetFiledMapping()
}
