package user

import (
	"context"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"sase_cloud_api/internal/common/consts"
	"sase_cloud_api/internal/config"
	"sase_cloud_api/internal/resource"
	"sase_cloud_api/pkg/logger"
	"sase_cloud_api/pkg/utils/rmemail"
)

type OrganizationUser struct {
	ProductID             string     `bson:"product_id"` // 产品ID
	OrgCode               string     `bson:"org_code"`
	UserCode              string     `bson:"user_code" json:"user_code"`
	UserName              string     `bson:"user_name" json:"user_name"`
	Account               string     `bson:"account" json:"account"`
	SourceType            string     `bson:"source_type"`
	SourceCode            string     `bson:"source_code"`
	Mobile                string     `bson:"mobile" json:"mobile"`
	Email                 string     `bson:"email" json:"email"`
	Position              string     `bson:"position" json:"position"`
	Password              string     `bson:"password"`
	ValidTime             int64      `bson:"valid_time"`
	IsActivationEmailSent bool       `bson:"is_activation_email_sent"`
	Status                int64      `bson:"status"`             // 状态 0: 正常 1: 暂停使用 2: 锁定 3：已过期
	BindDeviceStatus      int64      `bson:"bind_device_status"` // 绑定设备状态 0-尚未已注册，1-已注册
	CreatedAt             int64      `bson:"created_at"`
	UpdatedAt             int64      `bson:"updated_at"`
	DeptRoles             []string   `bson:"dept_roles"` // 部门角色列表
	UserRoles             []string   `bson:"user_roles"` // 用户角色列表
	DeptPath              [][]string `bson:"dept_path"`  // 部门路径
	IsActive              bool       `bson:"is_active"`  // 是否激活
}

type FieldMapping struct {
	Key               string   `json:"key"`
	SystemField       string   `json:"systemField"`
	SystemFieldText   string   `json:"systemFieldText"`
	DisplayNameFields []string `json:"displayNameFields,omitempty"`
	ThirdPartyField   string   `json:"thirdPartyField,omitempty"`
	Required          bool     `json:"required"`
	Tooltip           string   `json:"tooltip,omitempty"`
	Deleteable        bool     `json:"deleteable"`
}

type LdapConfig struct {
	Field     string `json:"field"`
	FieldText string `json:"field_text"`
}

type CreateUserRequest struct {
	ProductID             string     `bson:"product_id"` // 产品ID
	OrgCode               string     `bson:"org_code"`
	UserCode              string     `bson:"user_code"`
	UserName              string     `bson:"user_name"`
	Account               string     `bson:"account"`
	SourceType            string     `bson:"source_type"`
	SourceCode            string     `bson:"source_code"`
	Mobile                string     `bson:"mobile"`
	Email                 string     `bson:"email"`
	Position              string     `bson:"position"`
	Password              string     `bson:"password"`
	ValidTime             int64      `bson:"valid_time"`
	IsActivationEmailSent bool       `bson:"is_activation_email_sent"`
	Status                int64      `bson:"status"`             // 状态 0: 正常 1: 暂停使用 2: 锁定 3：已过期
	BindDeviceStatus      int64      `bson:"bind_device_status"` // 绑定设备状态 0-尚未注册，1-已注册
	CreatedAt             int64      `bson:"created_at"`
	UpdatedAt             int64      `bson:"updated_at"`
	DeptRole              string     `bson:"dept_role"`
	DeptPath              [][]string `bson:"dept_path"` // 部门路径
	IsActive              bool       `bson:"is_active"` // 是否激活
}

type UpdateUserRequest struct {
	ProductID             string     `bson:"product_id"` // 产品ID
	UserCode              string     `bson:"user_code"`
	UserName              string     `bson:"user_name"`
	Account               string     `bson:"account"`
	Mobile                string     `bson:"mobile"`
	Email                 string     `bson:"email"`
	Position              string     `bson:"position"`
	Password              string     `bson:"password"`
	ValidTime             int64      `bson:"valid_time"`
	IsActivationEmailSent bool       `bson:"is_activation_email_sent"`
	UpdatedAt             int64      `bson:"updated_at"`
	DeptPath              [][]string `bson:"dept_path"` // 部门路径
}

type SearchUserRequest struct {
	ProductID    string   `bson:"product_id"` // 产品ID
	OrgCode      string   `bson:"org_code"`
	OrgCodes     []string `bson:"org_codes"` // 组织编码列表
	UserCode     string   `bson:"user_code"`
	UserName     string   `bson:"user_name"`
	Account      string   `bson:"account"`
	Mobile       string   `bson:"mobile"`
	Email        string   `bson:"email"`
	Position     string   `bson:"position"`
	DeptCode     string   `bson:"dept_code"`     // 部门编码
	DeptCodes    []string `bson:"dept_codes"`    // 部门编码列表
	Status       int64    `bson:"status"`        // 状态 0: 正常 1: 暂停使用 2: 锁定 3：已过期
	ActiveStatus int64    `bson:"active_status"` // 激活状态 0: 未激活 1: 已激活
	DeptRole     string   `bson:"dept_role"`     // 部门角色
	Page         int64    `bson:"page"`
	Limit        int64    `bson:"limit"`
}

type DeleteUserRequest struct {
	ProductID string `bson:"product_id"` // 产品ID
	UserCode  string `bson:"user_code"`
	OrgCode   string `bson:"org_code"`
}

type GetUserDetailRequest struct {
	ProductID string `bson:"product_id"` // 产品ID
	UserCode  string `bson:"user_code"`
	OrgCode   string `bson:"org_code"`
}

type BatchUpdateUserDeptPathRequest struct {
	ProductID string     `bson:"product_id"` // 产品ID
	OrgCode   string     `bson:"org_code"`   // 组织编码
	UserCodes []string   `bson:"user_codes"` // 用户编码列表
	DeptPath  [][]string `bson:"dept_path"`  // 部门路径
}

type BatchUpdateUserDeptRolesRequest struct {
	ProductID string   `bson:"product_id"` // 产品ID
	OrgCode   string   `bson:"org_code"`   // 组织编码
	UserCodes []string `bson:"user_codes"` // 用户编码列表
	DeptRole  string   `bson:"dept_role"`  // 部门角色
}

type BatchUpdateUserStatusRequest struct {
	ProductID string   `bson:"product_id"` // 产品ID
	OrgCode   string   `bson:"org_code"`   // 组织编码
	UserCodes []string `bson:"user_codes"` // 用户编码列表
	Status    int64    `bson:"status"`     // 状态 0: 正常 1: 暂停使用 2: 锁定 3：已过期
}

type BatchDeleteUserRequest struct {
	ProductID string   `bson:"product_id"` // 产品ID
	UserCodes []string `bson:"user_codes"` // 用户编码列表
	OrgCode   string   `bson:"org_code"`   // 组织编码
}

var (
	Status0 int64 = 0
	Status1 int64 = 1
	Status2 int64 = 2
)

var USERSTATUSMAP = map[int64]string{
	Status0: "正常",
	Status1: "暂停使用",
	Status2: "锁定",
}

type SendEmailStruct struct {
	Link       string `json:"link"`
	Email      string `json:"email"`
	UserName   string `json:"user_name"`
	Account    string `json:"account"`
	Password   string `json:"password"`
	SourceType string `json:"source_type"`
}

// Repository 用户仓储接口
type Repository interface {
	CreateUser(ctx context.Context, user *CreateUserRequest) error
	UpdateUser(ctx context.Context, user *UpdateUserRequest) error
	DeleteUser(ctx context.Context, user *DeleteUserRequest) error
	GetUserDetail(ctx context.Context, user *GetUserDetailRequest) (*OrganizationUser, error)
	GetUserByUserCode(ctx context.Context, productID string, userCode string) (*OrganizationUser, error)
	SearchUser(ctx context.Context, user *SearchUserRequest) ([]*OrganizationUser, int64, error)
	FindOne(filter interface{}) (*OrganizationUser, error)
	FindMany(ctx context.Context, filter interface{}, findOptions *options.FindOptions) ([]*OrganizationUser, error)
	SearchUserByFilter(ctx context.Context, filter map[string]interface{}) ([]*OrganizationUser, error)
	UpdateUserByFilter(ctx context.Context, filter map[string]interface{}, update map[string]interface{}) error
	BatchUpdateUserDeptPath(ctx context.Context, user *BatchUpdateUserDeptPathRequest) error
	BatchUpdateUserStatus(ctx context.Context, user *BatchUpdateUserStatusRequest) error
	BatchDeleteUser(ctx context.Context, user *BatchDeleteUserRequest) error
	GetUserAndDeptRoleInfoAggregate(ctx context.Context, req SearchUserRequest, page, limit int64) ([]OrganizationUser, int64, error)
	BatchUpdateUserDeptRolesAdd(ctx context.Context, user *BatchUpdateUserDeptRolesRequest) error
	BatchUpdateUserDeptRolesDelete(ctx context.Context, user *BatchUpdateUserDeptRolesRequest) error
	BatchCreateUsers(ctx context.Context, users []*CreateUserRequest) error
	BatchUpdateUsers(ctx context.Context, productID string, orgCode string, users []*UpdateUserRequest) error
	UpdateOne(ctx context.Context, filter interface{}, update interface{}) error
	SendEmail(c *gin.Context, sendEmailBusinessType int, sendData []*SendEmailStruct) (int, error)
}

type repository struct {
	userCollection *mongo.Collection
}

func NewRepository() Repository {
	return &repository{
		userCollection: resource.Database.Collection("organization_user"),
	}
}

func (r *repository) CreateUser(ctx context.Context, user *CreateUserRequest) error {
	user.CreatedAt = time.Now().Unix()
	user.UpdatedAt = time.Now().Unix()

	_, err := r.userCollection.InsertOne(ctx, user)
	if err != nil {
		return err
	}

	return nil
}

func (r *repository) UpdateUser(ctx context.Context, user *UpdateUserRequest) error {
	user.UpdatedAt = time.Now().Unix()
	filter := bson.M{"product_id": user.ProductID, "user_code": user.UserCode}
	update := bson.M{"$set": user}
	_, err := r.userCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) DeleteUser(ctx context.Context, user *DeleteUserRequest) error {
	_, err := r.userCollection.DeleteOne(ctx, bson.M{"product_id": user.ProductID, "user_code": user.UserCode})
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) GetUserDetail(ctx context.Context, user *GetUserDetailRequest) (*OrganizationUser, error) {
	var result OrganizationUser
	filter := bson.M{"product_id": user.ProductID, "user_code": user.UserCode}
	if user.OrgCode != "" {
		filter["org_code"] = user.OrgCode
	}
	err := r.userCollection.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *repository) SearchUser(ctx context.Context, user *SearchUserRequest) ([]*OrganizationUser, int64, error) {
	filter := bson.M{"product_id": user.ProductID}

	if user.UserCode != "" {
		filter["user_code"] = user.UserCode
	}
	if user.UserName != "" {
		filter["user_name"] = map[string]interface{}{
			"$regex":   user.UserName,
			"$options": "i",
		}
	}
	if user.OrgCode != "" {
		filter["org_code"] = user.OrgCode
	}
	if user.Account != "" {
		filter["account"] = map[string]interface{}{
			"$regex":   user.Account,
			"$options": "i",
		}
	}
	if user.Mobile != "" {
		filter["mobile"] = map[string]interface{}{
			"$regex":   user.Mobile,
			"$options": "i",
		}
	}
	if user.Email != "" {
		filter["email"] = map[string]interface{}{
			"$regex":   user.Email,
			"$options": "i",
		}
	}
	if user.Position != "" {
		filter["position"] = map[string]interface{}{
			"$regex":   user.Position,
			"$options": "i",
		}
	}
	if user.DeptCode != "" {
		filter["dept_path"] = map[string]interface{}{
			"$elemMatch": map[string]interface{}{
				"$elemMatch": map[string]interface{}{
					"$eq": user.DeptCode,
				},
			},
		}
	}

	if len(user.OrgCodes) > 0 {
		filter["org_code"] = map[string]interface{}{
			"$in": user.OrgCodes,
		}
	}

	if len(user.DeptCodes) > 0 {
		filter["dept_path"] = map[string]interface{}{
			"$elemMatch": map[string]interface{}{
				"$elemMatch": map[string]interface{}{
					"$in": user.DeptCodes,
				},
			},
		}
	}

	if user.DeptRole != "" {
		filter["dept_roles"] = map[string]interface{}{
			"$in": []string{user.DeptRole},
		}
	}

	if user.Status != -1 {
		filter["status"] = user.Status
	}

	if user.ActiveStatus != -1 {
		if user.ActiveStatus == 1 {
			filter["is_active"] = true
		} else {
			filter["is_active"] = false
		}
	}

	if user.DeptRole != "" {
		filter["dept_roles"] = map[string]interface{}{
			"$in": []string{user.DeptRole},
		}
	}

	total, err := r.userCollection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	skip := (user.Page - 1) * user.Limit
	opts := options.Find().
		SetSkip(skip).
		SetLimit(user.Limit).
		SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.userCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	var users []*OrganizationUser
	if err = cursor.All(ctx, &users); err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

func (r *repository) FindMany(ctx context.Context, filter interface{}, findOptions *options.FindOptions) ([]*OrganizationUser, error) {
	cursor, err := r.userCollection.Find(ctx, filter, findOptions)

	if err != nil {
		return nil, err
	}

	defer func(cursor *mongo.Cursor, ctx context.Context) {
		_ = cursor.Close(ctx)
	}(cursor, ctx)

	data := make([]*OrganizationUser, 0)

	if err = cursor.All(ctx, &data); err != nil {
		return nil, err
	}

	return data, nil
}

func (r *repository) FindOne(filter interface{}) (*OrganizationUser, error) {
	data := &OrganizationUser{}
	if err := r.userCollection.FindOne(context.Background(), filter).Decode(data); err != nil {
		return nil, err
	}
	return data, nil
}

func (r *repository) SearchUserByFilter(ctx context.Context, filter map[string]interface{}) ([]*OrganizationUser, error) {
	var users []*OrganizationUser
	cursor, err := r.userCollection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}

	return users, nil
}

func (r *repository) UpdateUserByFilter(ctx context.Context, filter map[string]interface{}, update map[string]interface{}) error {
	_, err := r.userCollection.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) GetUserByUserCode(ctx context.Context, productID string, userCode string) (*OrganizationUser, error) {
	var result OrganizationUser
	filter := bson.M{"product_id": productID, "user_code": userCode}
	err := r.userCollection.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *repository) BatchUpdateUserDeptPath(ctx context.Context, user *BatchUpdateUserDeptPathRequest) error {
	filter := bson.M{"product_id": user.ProductID, "org_code": user.OrgCode, "user_code": bson.M{"$in": user.UserCodes}}
	update := bson.M{"$set": bson.M{"dept_path": user.DeptPath}}
	_, err := r.userCollection.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) GetUserAndDeptRoleInfoAggregate(ctx context.Context, req SearchUserRequest, page, limit int64) ([]OrganizationUser, int64, error) {
	skip := (page - 1) * limit
	// 构建聚合管道
	pipeline := []bson.M{
		{
			"$lookup": bson.M{
				"from": "organization_department_role_relation",
				"let": bson.M{
					"user_code":  "$user_code",
					"product_id": "$product_id",
				},
				"pipeline": []bson.M{
					{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": []bson.M{
									{"$eq": []string{"$user_code", "$$user_code"}},
									{"$eq": []string{"$product_id", "$$product_id"}},
								},
							},
						},
					},
				},
				"as": "role",
			},
		},
		{
			"$unwind": "$role",
		},
	}

	var filter []bson.M
	filter = append(filter, bson.M{
		"org_code":   req.OrgCode,
		"product_id": req.ProductID,
	})
	// 添加用户名搜索条件
	if req.UserName != "" {
		filter = append(filter, bson.M{
			"user_name": bson.M{
				"$regex": req.UserName,
			},
		})
	}

	if req.Mobile != "" {
		filter = append(filter, bson.M{
			"mobile": bson.M{
				"$regex": req.Mobile,
			},
		})
	}

	if req.Email != "" {
		filter = append(filter, bson.M{
			"email": bson.M{
				"$regex": req.Email,
			},
		})
	}

	// 添加账号状态搜索条件
	if req.Status != -1 {
		filter = append(filter, bson.M{
			"status": req.Status,
		})
	}
	// 添加使用状态搜索条件
	if req.ActiveStatus != -1 {
		filter = append(filter, bson.M{
			"is_active": req.ActiveStatus,
		})
	}

	// 添加部门搜索条件
	if req.DeptCode != "" {
		filter = append(filter, bson.M{
			"dept_path": bson.M{
				"$elemMatch": bson.M{
					"$elemMatch": bson.M{
						"$eq": req.DeptCode,
					},
				},
			},
		})
	}

	// 添加部门角色搜索条件
	if req.DeptRole != "" {
		filter = append(filter, bson.M{
			"role.role_code": req.DeptRole,
		})
	}

	pipeline = append(pipeline, bson.M{
		"$match": bson.M{
			"$and": filter,
		},
	})

	// 先进行计数
	countPipeline := append(pipeline, bson.M{
		"$count": "total",
	})

	// 执行计数查询
	countCursor, err := r.userCollection.Aggregate(ctx, countPipeline)
	if err != nil {
		return nil, 0, err
	}
	defer countCursor.Close(ctx)

	var countResult []bson.M
	if err = countCursor.All(ctx, &countResult); err != nil {
		return nil, 0, err
	}

	var total int64
	if len(countResult) > 0 {
		total = int64(countResult[0]["total"].(int32))
	}

	// 添加分页和项目字段
	pipeline = append(pipeline, bson.M{
		"$skip": skip,
	}, bson.M{
		"$limit": limit,
	}, bson.M{
		"$project": bson.M{
			"_id":       1,
			"user_code": 1,
			"org_code":  1,
			"user_name": 1,
			"dept_path": 1,
			"account":   1,
			"mobile":    1,
			"email":     1,
			"position":  1,
		},
	})

	// 执行数据查询
	cursor, err := r.userCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// 处理结果
	var orgUsers []OrganizationUser
	if err = cursor.All(ctx, &orgUsers); err != nil {
		return nil, 0, err
	}
	return orgUsers, total, nil

}

func (r *repository) BatchUpdateUserDeptRolesAdd(ctx context.Context, user *BatchUpdateUserDeptRolesRequest) error {
	filter := bson.M{"product_id": user.ProductID, "org_code": user.OrgCode, "user_code": bson.M{"$in": user.UserCodes}}
	update := bson.M{"$addToSet": bson.M{"dept_roles": user.DeptRole}}
	_, err := r.userCollection.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) BatchUpdateUserDeptRolesDelete(ctx context.Context, user *BatchUpdateUserDeptRolesRequest) error {
	filter := bson.M{"product_id": user.ProductID, "org_code": user.OrgCode, "user_code": bson.M{"$in": user.UserCodes}}
	update := bson.M{"$pull": bson.M{"dept_roles": user.DeptRole}}
	_, err := r.userCollection.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) BatchUpdateUserStatus(ctx context.Context, user *BatchUpdateUserStatusRequest) error {
	filter := bson.M{"product_id": user.ProductID, "org_code": user.OrgCode, "user_code": bson.M{"$in": user.UserCodes}}
	update := bson.M{"$set": bson.M{"status": user.Status}}
	_, err := r.userCollection.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) BatchCreateUsers(ctx context.Context, users []*CreateUserRequest) error {
	var userDocs []interface{}
	for _, user := range users {
		userDocs = append(userDocs, user)
	}
	_, err := r.userCollection.InsertMany(ctx, userDocs)
	if err != nil {
		return err
	}
	return nil
}

// 批量更新用户
func (r *repository) BatchUpdateUsers(ctx context.Context, productID string, orgCode string, users []*UpdateUserRequest) error {
	if len(users) == 0 {
		return nil
	}

	// 使用 BulkWrite 来执行批量操作
	models := make([]mongo.WriteModel, len(users))
	for i, user := range users {
		models[i] = mongo.NewUpdateOneModel().SetFilter(bson.M{"product_id": productID, "org_code": orgCode, "user_code": user.UserCode}).SetUpdate(bson.M{"$set": user})
	}

	opts := options.BulkWrite().SetOrdered(false)
	_, err := r.userCollection.BulkWrite(ctx, models, opts)
	return err
}

func (r *repository) BatchDeleteUser(ctx context.Context, user *BatchDeleteUserRequest) error {
	filter := bson.M{"product_id": user.ProductID, "org_code": user.OrgCode, "user_code": bson.M{"$in": user.UserCodes}}
	_, err := r.userCollection.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) UpdateOne(ctx context.Context, filter interface{}, update interface{}) error {
	_, err := r.userCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) SendEmail(c *gin.Context, sendEmailBusinessType int, sendData []*SendEmailStruct) (int, error) {
	var sendFailCount int
	emailConf := consts.EmailSubjectConf[c.GetString("language")]
	subject := emailConf["subject"]
	productName := emailConf["productName"]
	email := rmemail.NewRmEmail(rmemail.RmEmailConfig(config.Get().Email))

	for _, item := range sendData {
		if item.Email == "" {
			sendFailCount++
			continue
		}

		templateInfo, ok := consts.UserInviteSendEmailTemplate[sendEmailBusinessType][c.GetString("language")]
		if !ok {
			continue
		}

		emailContent, ok := templateInfo[item.SourceType]
		if !ok {
			emailContent = templateInfo["default"]
		}

		emailContent = strings.NewReplacer(
			"{{productName}}", productName,
			"{{link}}", item.Link,
			"{{username}}", item.UserName,
			"{{account}}", item.Account,
		).Replace(emailContent)

		if item.SourceType == consts.SourceTypeCustom {
			emailContent = strings.ReplaceAll(emailContent, "{{password}}", item.Password)
		}

		go func(emailContent string, item *SendEmailStruct) {
			if err := email.SendEmail(emailContent, subject, []string{item.Email}, nil, nil); err != nil {
				sendFailCount++
				logger.Errorf("SendEmail error: %v", err)
			}
		}(emailContent, item)
	}

	return sendFailCount, nil
}
